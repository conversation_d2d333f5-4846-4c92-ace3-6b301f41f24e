# سجل التغييرات | Changelog

جميع التغييرات المهمة في هذا المشروع سيتم توثيقها في هذا الملف.

All notable changes to this project will be documented in this file.

## [1.0.0] - 2024-01-01

### إضافات جديدة | Added
- **تكامل أجهزة البصمة**: دعم كامل لأجهزة ZKTeco وأجهزة أخرى متوافقة
- **المزامنة التلقائية**: مزامنة تلقائية للبيانات كل فترة زمنية محددة
- **المزامنة اليدوية**: معالج للمزامنة اليدوية مع خيارات متقدمة
- **إدارة الأجهزة**: واجهة شاملة لإدارة إعدادات أجهزة البصمة
- **سجل العمليات**: تسجيل مفصل لجميع عمليات المزامنة والأخطاء
- **نظام الصلاحيات**: مجموعات مستخدمين متخصصة (مستخدم/مدير البصمة)
- **دعم متعدد الشركات**: يدعم البيئات متعددة الشركات
- **الترجمة**: دعم كامل للعربية والإنجليزية
- **اختبار الاتصال**: إمكانية اختبار الاتصال مع الأجهزة
- **التنظيف التلقائي**: تنظيف تلقائي للسجلات القديمة

### المميزات التقنية | Technical Features
- **بروتوكول TCP/IP**: اتصال مباشر عبر الشبكة المحلية
- **مكتبة pyzk**: استخدام مكتبة Python متخصصة لأجهزة ZKTeco
- **المهام المجدولة**: مهام cron للمزامنة التلقائية والصيانة
- **التحقق من البيانات**: تحقق شامل من صحة البيانات المدخلة
- **معالجة الأخطاء**: معالجة متقدمة للأخطاء مع رسائل واضحة
- **الأمان**: تشفير كلمات المرور وحماية البيانات الحساسة

### النماذج | Models
- `fingerprint.device.config`: إعدادات أجهزة البصمة
- `fingerprint.sync.log`: سجل عمليات المزامنة
- `hr.employee`: إضافات للموظفين (معرف الجهاز، حالة البصمة)
- `hr.attendance`: إضافات لسجلات الحضور (معرف الجهاز، اسم الجهاز)

### المعالجات | Wizards
- `sync.attendance.wizard`: معالج المزامنة اليدوية
- `sync.attendance.result.wizard`: عرض نتائج المزامنة

### الواجهات | Views
- واجهات إدارة أجهزة البصمة (قائمة، نموذج، بحث)
- واجهات سجل المزامنة (قائمة، نموذج، بحث)
- إضافات واجهات الموظفين (تبويب إعدادات البصمة)
- قوائم التنقل المنظمة

### الأمان | Security
- مجموعة "Fingerprint User": عرض البيانات والمزامنة اليدوية
- مجموعة "Fingerprint Manager": إدارة كاملة للأجهزة والإعدادات
- قواعد أمان متعددة الشركات
- تشفير كلمات المرور

### التوثيق | Documentation
- دليل التثبيت التفصيلي (INSTALLATION.md)
- دليل الأمان الشامل (SECURITY.md)
- ملف README شامل بالعربية والإنجليزية
- تعليقات مفصلة في الكود
- اختبارات وحدة شاملة

### الاختبارات | Tests
- اختبارات إعدادات الأجهزة
- اختبارات سجل المزامنة
- اختبارات إضافات الموظفين
- اختبارات التحقق من البيانات
- اختبارات الاتصال والمزامنة

## المتطلبات | Requirements

### النظام | System
- Odoo 15 Community Edition
- Python 3.7+
- PostgreSQL 10+
- Ubuntu 18.04+ / CentOS 7+ / Windows Server 2016+

### المكتبات | Libraries
- pyzk >= 0.9.0
- odoo >= 15.0

### الشبكة | Network
- اتصال LAN مع أجهزة البصمة
- منفذ TCP (عادة 4370)
- عنوان IP ثابت للأجهزة

## التثبيت السريع | Quick Installation

```bash
# 1. تثبيت المكتبة المطلوبة
pip install pyzk

# 2. نسخ الموديول
cp -r fingerprint_attendance /path/to/odoo/addons/

# 3. إعادة تشغيل أودوو
sudo systemctl restart odoo

# 4. تثبيت الموديول من واجهة أودوو
# Apps > Search "Fingerprint" > Install
```

## الإعداد السريع | Quick Setup

```python
# 1. إنشاء إعداد جهاز
device = env['fingerprint.device.config'].create({
    'name': 'Main Device',
    'device_ip': '*************',
    'device_port': 4370,
    'auto_sync': True,
    'sync_interval': 5,
})

# 2. اختبار الاتصال
device.test_connection()

# 3. ربط موظف
employee = env['hr.employee'].search([('name', '=', 'Employee Name')])
employee.write({'device_user_id': '001'})

# 4. تشغيل المزامنة
device.sync_attendance_data()
```

## الدعم | Support

### المشاكل الشائعة | Common Issues
1. **خطأ pyzk**: تأكد من تثبيت المكتبة `pip install pyzk`
2. **خطأ الاتصال**: تحقق من عنوان IP والمنفذ
3. **خطأ الصلاحيات**: تأكد من إضافة المستخدم لمجموعة البصمة

### الحصول على المساعدة | Getting Help
- راجع ملف README.md للتوثيق الكامل
- راجع ملف INSTALLATION.md لخطوات التثبيت التفصيلية
- راجع ملف SECURITY.md لإرشادات الأمان
- تحقق من سجلات النظام للأخطاء التفصيلية

## الترخيص | License

هذا المشروع مرخص تحت رخصة LGPL-3.0

This project is licensed under the LGPL-3.0 License.

## المساهمة | Contributing

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع للميزة الجديدة
3. إضافة الاختبارات المناسبة
4. التأكد من اجتياز جميع الاختبارات
5. إرسال Pull Request

We welcome contributions! Please:
1. Fork the project
2. Create a feature branch
3. Add appropriate tests
4. Ensure all tests pass
5. Submit a Pull Request

---

**ملاحظة**: هذا الإصدار الأول (1.0.0) ويتضمن جميع المميزات الأساسية المطلوبة.

**Note**: This is the first release (1.0.0) and includes all required basic features.
