# -*- coding: utf-8 -*-

from odoo import models, fields, api, _


class FingerprintSyncLog(models.Model):
    _name = 'fingerprint.sync.log'
    _description = 'Fingerprint Synchronization Log'
    _order = 'sync_date desc'
    _rec_name = 'display_name'

    device_config_id = fields.Many2one(
        'fingerprint.device.config',
        string='Device',
        required=True,
        ondelete='cascade',
        help='Fingerprint device configuration'
    )
    sync_date = fields.Datetime(
        string='Sync Date',
        required=True,
        default=fields.Datetime.now,
        help='Date and time of synchronization'
    )
    status = fields.Selection([
        ('success', 'Success'),
        ('warning', 'Warning'),
        ('error', 'Error'),
        ('info', 'Info'),
    ], string='Status', required=True, help='Synchronization status')
    
    message = fields.Text(
        string='Message',
        required=True,
        help='Synchronization message or error description'
    )
    details = fields.Text(
        string='Details',
        help='Additional details about the synchronization process'
    )
    company_id = fields.Many2one(
        'res.company',
        string='Company',
        related='device_config_id.company_id',
        store=True,
        readonly=True
    )
    
    display_name = fields.Char(
        string='Display Name',
        compute='_compute_display_name',
        store=True
    )

    @api.depends('device_config_id', 'sync_date', 'status')
    def _compute_display_name(self):
        """Compute display name for the log entry"""
        for record in self:
            if record.device_config_id and record.sync_date:
                record.display_name = _('%s - %s (%s)') % (
                    record.device_config_id.name,
                    record.sync_date.strftime('%Y-%m-%d %H:%M:%S'),
                    record.status.title()
                )
            else:
                record.display_name = _('Sync Log')

    def name_get(self):
        """Override name_get to show meaningful names"""
        result = []
        for record in self:
            result.append((record.id, record.display_name))
        return result

    @api.model
    def cleanup_old_logs(self, days=30):
        """Clean up old log entries (called by cron job)"""
        from datetime import timedelta
        cutoff_date = fields.Datetime.now() - timedelta(days=days)
        old_logs = self.search([('sync_date', '<', cutoff_date)])
        count = len(old_logs)
        old_logs.unlink()
        return count
