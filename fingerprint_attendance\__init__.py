# -*- coding: utf-8 -*-

from . import models
from . import wizard

def post_init_hook(cr, registry):
    """Post-installation hook to set up default configurations"""
    from odoo import api, SUPERUSER_ID
    
    env = api.Environment(cr, SUPERUSER_ID, {})
    
    # Create default fingerprint device configuration if none exists
    device_config = env['fingerprint.device.config'].search([], limit=1)
    if not device_config:
        env['fingerprint.device.config'].create({
            'name': 'Default Fingerprint Device',
            'device_ip': '*************',
            'device_port': 4370,
            'sync_interval': 5,
            'auto_sync': True,
            'active': True,
        })

def uninstall_hook(cr, registry):
    """Pre-uninstallation hook to clean up"""
    from odoo import api, SUPERUSER_ID
    
    env = api.Environment(cr, SUPERUSER_ID, {})
    
    # Disable all cron jobs related to this module
    cron_jobs = env['ir.cron'].search([
        ('model_id.model', '=', 'fingerprint.device.config')
    ])
    cron_jobs.write({'active': False})
