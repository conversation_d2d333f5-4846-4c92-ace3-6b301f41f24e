# -*- coding: utf-8 -*-

import logging
from datetime import datetime, timedelta
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError

_logger = logging.getLogger(__name__)

try:
    from zk import ZK
    ZK_AVAILABLE = True
except ImportError:
    ZK_AVAILABLE = False
    _logger.warning("pyzk library not found. Please install it using: pip install pyzk")


class FingerprintDeviceConfig(models.Model):
    _name = 'fingerprint.device.config'
    _description = 'Fingerprint Device Configuration'
    _rec_name = 'name'

    name = fields.Char(
        string='Device Name',
        required=True,
        help='Name to identify the fingerprint device'
    )
    device_ip = fields.Char(
        string='Device IP Address',
        required=True,
        help='IP address of the fingerprint device'
    )
    device_port = fields.Integer(
        string='Port',
        default=4370,
        required=True,
        help='Port number for device connection'
    )
    username = fields.Char(
        string='Username',
        help='Username for device authentication (if required)'
    )
    password = fields.Char(
        string='Password',
        help='Password for device authentication (if required)'
    )
    timeout = fields.Integer(
        string='Connection Timeout',
        default=60,
        help='Connection timeout in seconds'
    )
    sync_interval = fields.Integer(
        string='Sync Interval (minutes)',
        default=5,
        required=True,
        help='Automatic synchronization interval in minutes'
    )
    auto_sync = fields.Boolean(
        string='Enable Auto Sync',
        default=True,
        help='Enable automatic synchronization'
    )
    last_sync_date = fields.Datetime(
        string='Last Sync Date',
        readonly=True,
        help='Last successful synchronization date'
    )
    active = fields.Boolean(
        string='Active',
        default=True,
        help='Enable/disable this device configuration'
    )
    company_id = fields.Many2one(
        'res.company',
        string='Company',
        default=lambda self: self.env.company,
        required=True
    )
    
    # Statistics fields
    total_syncs = fields.Integer(
        string='Total Syncs',
        readonly=True,
        default=0,
        help='Total number of successful synchronizations'
    )
    last_error = fields.Text(
        string='Last Error',
        readonly=True,
        help='Last synchronization error message'
    )
    
    @api.constrains('device_ip')
    def _check_device_ip(self):
        """Validate IP address format"""
        import re
        ip_pattern = re.compile(r'^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$')
        for record in self:
            if record.device_ip and not ip_pattern.match(record.device_ip):
                raise ValidationError(_('Please enter a valid IP address'))
    
    @api.constrains('device_port')
    def _check_device_port(self):
        """Validate port number"""
        for record in self:
            if record.device_port and (record.device_port < 1 or record.device_port > 65535):
                raise ValidationError(_('Port number must be between 1 and 65535'))
    
    @api.constrains('sync_interval')
    def _check_sync_interval(self):
        """Validate sync interval"""
        for record in self:
            if record.sync_interval and record.sync_interval < 1:
                raise ValidationError(_('Sync interval must be at least 1 minute'))

    def _safe_get_device_time(self, conn):
        """Safely get device time without causing date errors"""
        try:
            # Try to get device time
            device_time = conn.get_time()
            if device_time:
                return device_time
        except Exception as e:
            _logger.warning("Could not get device time: %s", str(e))

        # Return current time as fallback
        return datetime.now()

    def _safe_parse_timestamp(self, timestamp):
        """Safely parse timestamp from device"""
        if not timestamp:
            return None

        try:
            # If it's already a datetime object
            if isinstance(timestamp, datetime):
                # Validate the datetime
                if timestamp.year < 1900 or timestamp.year > 2100:
                    _logger.warning("Invalid year in timestamp: %s", timestamp)
                    return None
                return timestamp

            # If it's a string, try to parse it
            if isinstance(timestamp, str):
                # Clean the string first
                timestamp = timestamp.strip()
                if not timestamp:
                    return None

                # Try different date formats
                formats = [
                    '%Y-%m-%d %H:%M:%S',
                    '%Y/%m/%d %H:%M:%S',
                    '%d/%m/%Y %H:%M:%S',
                    '%Y-%m-%d',
                    '%Y/%m/%d',
                    '%d/%m/%Y',
                    '%m/%d/%Y %H:%M:%S',
                    '%m/%d/%Y'
                ]

                for fmt in formats:
                    try:
                        parsed_date = datetime.strptime(timestamp, fmt)
                        # Validate the parsed date
                        if parsed_date.year < 1900 or parsed_date.year > 2100:
                            continue
                        return parsed_date
                    except ValueError:
                        continue

                # If all formats fail, log and return None
                _logger.warning("Could not parse timestamp: %s", timestamp)
                return None

            # For numeric types, try to convert
            if isinstance(timestamp, (int, float)):
                try:
                    # Handle Unix timestamp
                    parsed_date = datetime.fromtimestamp(float(timestamp))
                    if parsed_date.year < 1900 or parsed_date.year > 2100:
                        _logger.warning("Invalid year in timestamp: %s", parsed_date)
                        return None
                    return parsed_date
                except (ValueError, OSError) as e:
                    _logger.warning("Could not convert numeric timestamp %s: %s", timestamp, str(e))
                    return None

            # For other types, try string conversion first
            try:
                return self._safe_parse_timestamp(str(timestamp))
            except:
                _logger.warning("Could not convert timestamp type %s: %s", type(timestamp), timestamp)
                return None

        except Exception as e:
            _logger.warning("Error parsing timestamp %s: %s", timestamp, str(e))
            return None

    def _validate_attendance_record(self, attendance):
        """Validate attendance record before processing"""
        try:
            # Check if attendance object exists
            if not attendance:
                return False, "Empty attendance record"

            # Check if user_id exists
            if not hasattr(attendance, 'user_id') or not attendance.user_id:
                return False, "Missing user_id"

            # Check if timestamp exists
            if not hasattr(attendance, 'timestamp') or not attendance.timestamp:
                return False, "Missing timestamp"

            # Try to parse timestamp
            parsed_timestamp = self._safe_parse_timestamp(attendance.timestamp)
            if not parsed_timestamp:
                return False, f"Invalid timestamp: {attendance.timestamp}"

            return True, "Valid record"

        except Exception as e:
            return False, f"Validation error: {str(e)}"

    def test_connection(self):
        """Test connection to fingerprint device"""
        self.ensure_one()

        if not ZK_AVAILABLE:
            raise UserError(_('pyzk library is not installed. Please install it using: pip install pyzk'))

        try:
            # Create ZK instance
            zk = ZK(self.device_ip, port=self.device_port, timeout=self.timeout)

            # Connect to device
            conn = zk.connect()

            if conn:
                # Get device info safely
                device_info = {}
                try:
                    device_info['firmware_version'] = conn.get_firmware_version() or 'Unknown'
                except:
                    device_info['firmware_version'] = 'Unknown'

                try:
                    device_info['serialnumber'] = conn.get_serialnumber() or 'Unknown'
                except:
                    device_info['serialnumber'] = 'Unknown'

                try:
                    device_info['platform'] = conn.get_platform() or 'Unknown'
                except:
                    device_info['platform'] = 'Unknown'

                try:
                    device_info['device_name'] = conn.get_device_name() or 'Unknown'
                except:
                    device_info['device_name'] = 'Unknown'

                try:
                    users = conn.get_users()
                    device_info['users_count'] = len(users) if users else 0
                except:
                    device_info['users_count'] = 0

                try:
                    attendances = conn.get_attendance()
                    device_info['attendance_count'] = len(attendances) if attendances else 0
                except:
                    device_info['attendance_count'] = 0

                # Disconnect
                conn.disconnect()

                # Log successful connection
                self._create_sync_log('success', _('Connection test successful'), device_info)

                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Connection Successful'),
                        'message': _('Successfully connected to device: %s\nUsers: %d, Records: %d') % (
                            device_info.get('device_name', 'Unknown'),
                            device_info.get('users_count', 0),
                            device_info.get('attendance_count', 0)
                        ),
                        'type': 'success',
                        'sticky': False,
                    }
                }
            else:
                raise UserError(_('Failed to connect to device'))

        except Exception as e:
            error_msg = str(e)
            self._create_sync_log('error', _('Connection test failed: %s') % error_msg)
            raise UserError(_('Connection failed: %s') % error_msg)

    def sync_attendance_data(self):
        """Synchronize attendance data from fingerprint device"""
        self.ensure_one()

        if not ZK_AVAILABLE:
            raise UserError(_('pyzk library is not installed. Please install it using: pip install pyzk'))

        try:
            # Create ZK instance
            zk = ZK(self.device_ip, port=self.device_port, timeout=self.timeout)

            # Connect to device
            conn = zk.connect()

            if not conn:
                raise UserError(_('Failed to connect to device'))

            # Get attendance data safely
            attendances = []
            try:
                attendances = conn.get_attendance()
                if not attendances:
                    attendances = []
            except Exception as e:
                _logger.warning("Could not get attendance data: %s", str(e))
                attendances = []

            # Get users data for mapping safely
            users = []
            user_mapping = {}
            try:
                users = conn.get_users()
                if users:
                    user_mapping = {user.user_id: user for user in users}
            except Exception as e:
                _logger.warning("Could not get users data: %s", str(e))
                user_mapping = {}

            # Disconnect from device
            conn.disconnect()

            # Process attendance data
            processed_count = 0
            error_count = 0
            skipped_count = 0

            for attendance in attendances:
                try:
                    # Validate attendance record first
                    is_valid, validation_message = self._validate_attendance_record(attendance)
                    if not is_valid:
                        skipped_count += 1
                        _logger.warning("Skipping invalid attendance record: %s", validation_message)
                        continue

                    # Process the record
                    result = self._process_attendance_record(attendance, user_mapping)
                    if result is True:  # Successfully processed
                        processed_count += 1
                    elif result is None:  # Skipped (duplicate)
                        skipped_count += 1
                    else:  # result is False - error
                        error_count += 1

                except Exception as e:
                    error_count += 1
                    _logger.error("Error processing attendance record: %s", str(e))

            # Update sync statistics
            self.write({
                'last_sync_date': fields.Datetime.now(),
                'total_syncs': self.total_syncs + 1,
                'last_error': False if error_count == 0 else _('%d records failed to process') % error_count
            })

            # Create sync log
            total_records = len(attendances)
            message = _('Sync completed. Total: %d, Processed: %d, Skipped: %d, Errors: %d') % (
                total_records, processed_count, skipped_count, error_count
            )

            log_status = 'success'
            if error_count > 0:
                log_status = 'warning'
            if processed_count == 0 and total_records > 0:
                log_status = 'error'

            self._create_sync_log(log_status, message, {
                'total_records': total_records,
                'processed': processed_count,
                'skipped': skipped_count,
                'errors': error_count,
                'users_found': len(user_mapping)
            })

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Sync Completed'),
                    'message': message,
                    'type': 'success' if error_count == 0 else 'warning',
                    'sticky': False,
                }
            }

        except Exception as e:
            error_msg = str(e)
            self.write({'last_error': error_msg})
            self._create_sync_log('error', _('Sync failed: %s') % error_msg)
            raise UserError(_('Sync failed: %s') % error_msg)

    def _process_attendance_record(self, attendance, user_mapping):
        """Process a single attendance record"""
        try:
            # Get user info
            user_info = user_mapping.get(attendance.user_id)
            if not user_info:
                _logger.warning("User ID %s not found in device users", attendance.user_id)
                return False

            # Find employee by device user ID
            employee = self.env['hr.employee'].search([
                ('device_user_id', '=', str(attendance.user_id))
            ], limit=1)

            if not employee:
                _logger.warning("Employee not found for device user ID: %s", attendance.user_id)
                return False

            # Safely get timestamp
            timestamp = None
            if hasattr(attendance, 'timestamp'):
                timestamp = self._safe_parse_timestamp(attendance.timestamp)

            if not timestamp:
                _logger.warning("Could not get valid timestamp from attendance record")
                return False

            # Create unique identifier for this attendance record
            attendance_id = f"{attendance.user_id}_{timestamp.strftime('%Y%m%d_%H%M%S')}"

            # Check if attendance record already exists
            existing_attendance = self.env['hr.attendance'].search([
                ('employee_id', '=', employee.id),
                ('check_in', '=', timestamp),
                ('device_attendance_id', '=', attendance_id)
            ], limit=1)

            if existing_attendance:
                return None  # Skip duplicate records (not an error)

            # Create attendance record
            attendance_vals = {
                'employee_id': employee.id,
                'check_in': timestamp,
                'device_attendance_id': attendance_id,
                'device_name': self.name,
            }

            # Determine if this is check-in or check-out based on punch type
            # Punch type: 0=check-in, 1=check-out, 2=break-out, 3=break-in, 4=overtime-in, 5=overtime-out
            if hasattr(attendance, 'punch') and attendance.punch in [1, 2, 5]:  # Check-out types
                # Find the last check-in without check-out for this employee
                last_attendance = self.env['hr.attendance'].search([
                    ('employee_id', '=', employee.id),
                    ('check_out', '=', False)
                ], order='check_in desc', limit=1)

                if last_attendance:
                    last_attendance.write({'check_out': timestamp})
                    return True  # Successfully processed as check-out

            # Create new attendance record
            self.env['hr.attendance'].create(attendance_vals)
            return True  # Successfully processed as check-in

        except Exception as e:
            _logger.error("Error processing attendance record: %s", str(e))
            return False

    def _create_sync_log(self, status, message, details=None):
        """Create a synchronization log entry"""
        self.env['fingerprint.sync.log'].create({
            'device_config_id': self.id,
            'sync_date': fields.Datetime.now(),
            'status': status,
            'message': message,
            'details': str(details) if details else False,
        })

    def action_view_sync_logs(self):
        """View sync logs for this device"""
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': _('Sync Logs - %s') % self.name,
            'res_model': 'fingerprint.sync.log',
            'view_mode': 'tree,form',
            'domain': [('device_config_id', '=', self.id)],
            'context': {
                'default_device_config_id': self.id,
                'search_default_device_config_id': self.id,
            },
        }

    @api.model
    def cron_sync_attendance(self):
        """Cron job to automatically sync attendance data"""
        active_devices = self.search([
            ('active', '=', True),
            ('auto_sync', '=', True)
        ])
        
        for device in active_devices:
            try:
                device.sync_attendance_data()
            except Exception as e:
                _logger.error("Auto sync failed for device %s: %s", device.name, str(e))
                device._create_sync_log('error', _('Auto sync failed: %s') % str(e))
