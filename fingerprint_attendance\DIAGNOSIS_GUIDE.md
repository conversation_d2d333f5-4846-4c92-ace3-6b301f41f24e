# دليل تشخيص أجهزة البصمة | Fingerprint Device Diagnosis Guide

## المشكلة الشائعة | Common Issue

```
Sync completed. Total: 0, Processed: 0, Skipped: 0, Errors: 0
```

هذا يعني أن الاتصال مع الجهاز يعمل لكن لا يتم جلب أي بيانات.

This means the connection to the device works but no data is being retrieved.

## أداة التشخيص الجديدة | New Diagnosis Tool

### استخدام أداة التشخيص | Using the Diagnosis Tool

1. **الذهاب إلى إعدادات الجهاز**
   - Fingerprint Attendance > Configuration > Devices
   - فتح إعداد الجهاز

2. **النقر على "Diagnose Device"**
   - زر جديد بجانب "Test Connection" و "Sync Now"
   - يقوم بفحص شامل للجهاز والبيانات

3. **مراجعة نتائج التشخيص**
   - تظهر نافذة بتفاصيل الفحص
   - يتم حفظ النتائج في Sync Logs

## نتائج التشخيص المتوقعة | Expected Diagnosis Results

### ✅ **حالة طبيعية | Normal State**
```
✓ ZK instance created successfully
✓ Connected to device successfully
✓ Device name: ZKTeco Device
✓ Firmware version: Ver 6.60
✓ Found 25 users on device
  - User 1: Ahmed Ali
  - User 2: Sara Mohamed
  - User 3: Omar Hassan
  - ... and 22 more users
✓ Found 150 attendance records on device
  - Record 1: User 1 at 2024-01-15 08:30:00
  - Record 2: User 2 at 2024-01-15 08:35:00
  - Record 3: User 1 at 2024-01-15 17:30:00
  - ... and 147 more records
✓ 20 out of 25 users are mapped to employees
⚠ Unmapped user IDs: 21, 22, 23, 24, 25
✓ Disconnected from device
```

### ⚠️ **مشاكل شائعة | Common Issues**

#### 1. لا توجد بيانات حضور
```
✓ Connected to device successfully
✓ Found 25 users on device
⚠ No attendance records found on device
```
**الحل**: التأكد من وجود بيانات حضور في الجهاز

#### 2. لا توجد مستخدمين
```
✓ Connected to device successfully
⚠ No users found on device
⚠ No attendance records found on device
```
**الحل**: تسجيل المستخدمين في الجهاز أولاً

#### 3. مستخدمين غير مربوطين
```
✓ Found 25 users on device
✓ Found 150 attendance records on device
⚠ 0 out of 25 users are mapped to employees
⚠ Unmapped user IDs: 1, 2, 3, 4, 5, ...
```
**الحل**: ربط الموظفين بمعرفات الجهاز

## خطوات حل المشاكل | Troubleshooting Steps

### 1. فحص بيانات الجهاز | Check Device Data

#### أ. التأكد من وجود مستخدمين في الجهاز
- استخدام واجهة الجهاز المباشرة
- التحقق من قائمة المستخدمين
- التأكد من تسجيل البصمات

#### ب. التأكد من وجود سجلات حضور
- فحص سجلات الحضور في الجهاز
- التأكد من وجود بيانات حديثة
- فحص إعدادات التخزين في الجهاز

### 2. ربط الموظفين | Link Employees

#### أ. الحصول على معرفات المستخدمين من التشخيص
```
✓ Found 25 users on device
  - User 1: Ahmed Ali
  - User 2: Sara Mohamed
  - User 3: Omar Hassan
```

#### ب. ربط الموظفين في أودوو
1. الذهاب إلى HR > Employees
2. فتح سجل الموظف
3. تبويب "Fingerprint Settings"
4. تعيين "Device User ID" = معرف المستخدم في الجهاز
5. تفعيل "Fingerprint Enrolled"

### 3. فحص إعدادات الجهاز | Check Device Settings

#### أ. إعدادات الاتصال
- التأكد من صحة عنوان IP
- التأكد من صحة رقم المنفذ
- فحص إعدادات الشبكة في الجهاز

#### ب. إعدادات البيانات
- التأكد من تفعيل تسجيل الحضور
- فحص إعدادات التخزين
- التأكد من عدم امتلاء ذاكرة الجهاز

### 4. اختبار المزامنة التدريجي | Gradual Sync Testing

#### أ. اختبار الاتصال أولاً
```bash
# النقر على "Test Connection"
# يجب أن يظهر: "Successfully connected to device"
```

#### ب. تشخيص الجهاز
```bash
# النقر على "Diagnose Device"
# مراجعة جميع النتائج
```

#### ج. اختبار المزامنة
```bash
# النقر على "Sync Now"
# مراجعة النتائج المفصلة
```

## رسائل التشخيص التفصيلية | Detailed Diagnosis Messages

### رسائل النجاح | Success Messages
- `✓ ZK instance created successfully` - تم إنشاء الاتصال
- `✓ Connected to device successfully` - تم الاتصال بالجهاز
- `✓ Device name: [name]` - تم الحصول على اسم الجهاز
- `✓ Found X users on device` - تم العثور على مستخدمين
- `✓ Found X attendance records` - تم العثور على سجلات حضور

### رسائل التحذير | Warning Messages
- `⚠ No users found on device` - لا توجد مستخدمين
- `⚠ No attendance records found` - لا توجد سجلات حضور
- `⚠ Unmapped user IDs: [list]` - مستخدمين غير مربوطين
- `⚠ Could not get [data]: [error]` - فشل في الحصول على بيانات

### رسائل الخطأ | Error Messages
- `✗ Failed to connect to device` - فشل الاتصال
- `✗ Could not get [data]: [error]` - خطأ في جلب البيانات
- `✗ Diagnosis failed: [error]` - فشل التشخيص

## حلول للمشاكل الشائعة | Solutions for Common Problems

### 1. "No attendance records found on device"

#### الأسباب المحتملة:
- الجهاز جديد ولم يتم تسجيل حضور بعد
- تم مسح بيانات الحضور من الجهاز
- مشكلة في إعدادات تسجيل الحضور

#### الحلول:
1. **تسجيل حضور تجريبي**:
   - استخدام البصمة على الجهاز
   - التأكد من ظهور رسالة نجاح
   - إعادة تشغيل التشخيص

2. **فحص إعدادات الجهاز**:
   - التأكد من تفعيل تسجيل الحضور
   - فحص إعدادات التاريخ والوقت
   - التأكد من عدم امتلاء الذاكرة

### 2. "No users found on device"

#### الأسباب المحتملة:
- لم يتم تسجيل أي مستخدمين في الجهاز
- مشكلة في قاعدة بيانات الجهاز
- خطأ في إعدادات الجهاز

#### الحلول:
1. **تسجيل مستخدمين جدد**:
   - استخدام واجهة الجهاز
   - تسجيل بصمة واحدة على الأقل
   - التأكد من حفظ البيانات

2. **إعادة تشغيل الجهاز**:
   - إيقاف وتشغيل الجهاز
   - التأكد من تحميل البيانات
   - إعادة اختبار الاتصال

### 3. "0 out of X users are mapped to employees"

#### الأسباب المحتملة:
- لم يتم ربط الموظفين بمعرفات الجهاز
- معرفات خاطئة في أودوو
- عدم تطابق معرفات الجهاز مع أودوو

#### الحلول:
1. **ربط الموظفين**:
   ```bash
   # لكل موظف في أودوو:
   # HR > Employees > [اسم الموظف]
   # تبويب "Fingerprint Settings"
   # Device User ID = معرف المستخدم من التشخيص
   ```

2. **التحقق من المعرفات**:
   - مقارنة معرفات التشخيص مع أودوو
   - التأكد من عدم وجود مسافات أو أحرف إضافية
   - استخدام نفس التنسيق (رقم أو نص)

## مراقبة الأداء | Performance Monitoring

### 1. سجلات التشخيص
- يتم حفظ نتائج التشخيص في Sync Logs
- يمكن مراجعتها لاحقاً
- تحتوي على تفاصيل تقنية

### 2. إحصائيات المزامنة
- عدد المستخدمين المكتشفين
- عدد سجلات الحضور
- عدد الموظفين المربوطين
- أخطاء وتحذيرات

### 3. التحسين المستمر
- مراجعة دورية للتشخيص
- تحديث ربط الموظفين
- مراقبة أداء الجهاز

---

## الخلاصة | Summary

أداة التشخيص الجديدة تساعد في:
- ✅ تحديد سبب عدم وجود بيانات
- ✅ فحص حالة الجهاز والاتصال
- ✅ التحقق من ربط الموظفين
- ✅ تقديم حلول واضحة للمشاكل

**استخدم "Diagnose Device" لفهم سبب المشكلة وحلها خطوة بخطوة.** 🔍
