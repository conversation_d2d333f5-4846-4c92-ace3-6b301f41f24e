# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* fingerprint_attendance
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-01 12:00+0000\n"
"PO-Revision-Date: 2024-01-01 12:00+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ar\n"

#. module: fingerprint_attendance
#: model:ir.model.fields,field_description:fingerprint_attendance.field_fingerprint_device_config__active
msgid "Active"
msgstr "نشط"

#. module: fingerprint_attendance
#: model:ir.model.fields,field_description:fingerprint_attendance.field_fingerprint_sync_log__sync_date
msgid "Sync Date"
msgstr "تاريخ المزامنة"

#. module: fingerprint_attendance
#: model:ir.model.fields,field_description:fingerprint_attendance.field_fingerprint_device_config__name
msgid "Device Name"
msgstr "اسم الجهاز"

#. module: fingerprint_attendance
#: model:ir.model.fields,field_description:fingerprint_attendance.field_fingerprint_device_config__device_ip
msgid "Device IP Address"
msgstr "عنوان IP للجهاز"

#. module: fingerprint_attendance
#: model:ir.model.fields,field_description:fingerprint_attendance.field_fingerprint_device_config__device_port
msgid "Port"
msgstr "المنفذ"

#. module: fingerprint_attendance
#: model:ir.model.fields,field_description:fingerprint_attendance.field_fingerprint_device_config__username
msgid "Username"
msgstr "اسم المستخدم"

#. module: fingerprint_attendance
#: model:ir.model.fields,field_description:fingerprint_attendance.field_fingerprint_device_config__password
msgid "Password"
msgstr "كلمة المرور"

#. module: fingerprint_attendance
#: model:ir.model.fields,field_description:fingerprint_attendance.field_fingerprint_device_config__timeout
msgid "Connection Timeout"
msgstr "مهلة الاتصال"

#. module: fingerprint_attendance
#: model:ir.model.fields,field_description:fingerprint_attendance.field_fingerprint_device_config__sync_interval
msgid "Sync Interval (minutes)"
msgstr "فترة المزامنة (بالدقائق)"

#. module: fingerprint_attendance
#: model:ir.model.fields,field_description:fingerprint_attendance.field_fingerprint_device_config__auto_sync
msgid "Enable Auto Sync"
msgstr "تفعيل المزامنة التلقائية"

#. module: fingerprint_attendance
#: model:ir.model.fields,field_description:fingerprint_attendance.field_fingerprint_device_config__last_sync_date
msgid "Last Sync Date"
msgstr "تاريخ آخر مزامنة"

#. module: fingerprint_attendance
#: model:ir.model.fields,field_description:fingerprint_attendance.field_fingerprint_device_config__total_syncs
msgid "Total Syncs"
msgstr "إجمالي المزامنات"

#. module: fingerprint_attendance
#: model:ir.model.fields,field_description:fingerprint_attendance.field_fingerprint_device_config__last_error
msgid "Last Error"
msgstr "آخر خطأ"

#. module: fingerprint_attendance
#: model:ir.model.fields,field_description:fingerprint_attendance.field_hr_employee__device_user_id
msgid "Device User ID"
msgstr "معرف المستخدم في الجهاز"

#. module: fingerprint_attendance
#: model:ir.model.fields,field_description:fingerprint_attendance.field_hr_employee__fingerprint_enrolled
msgid "Fingerprint Enrolled"
msgstr "البصمة مسجلة"

#. module: fingerprint_attendance
#: model:ir.model.fields,field_description:fingerprint_attendance.field_hr_employee__last_attendance_sync
msgid "Last Attendance Sync"
msgstr "آخر مزامنة حضور"

#. module: fingerprint_attendance
#: model:ir.model.fields,field_description:fingerprint_attendance.field_hr_attendance__device_attendance_id
msgid "Device Attendance ID"
msgstr "معرف الحضور في الجهاز"

#. module: fingerprint_attendance
#: model:ir.model.fields,field_description:fingerprint_attendance.field_hr_attendance__device_name
msgid "Device Name"
msgstr "اسم الجهاز"

#. module: fingerprint_attendance
#: model:ir.model.fields,field_description:fingerprint_attendance.field_hr_attendance__is_from_device
msgid "From Device"
msgstr "من الجهاز"

#. module: fingerprint_attendance
#: model:ir.actions.act_window,name:fingerprint_attendance.action_fingerprint_device_config
#: model:ir.ui.menu,name:fingerprint_attendance.menu_fingerprint_device_config
msgid "Fingerprint Devices"
msgstr "أجهزة البصمة"

#. module: fingerprint_attendance
#: model:ir.actions.act_window,name:fingerprint_attendance.action_fingerprint_sync_log
#: model:ir.ui.menu,name:fingerprint_attendance.menu_fingerprint_sync_log
msgid "Sync Logs"
msgstr "سجلات المزامنة"

#. module: fingerprint_attendance
#: model:ir.actions.act_window,name:fingerprint_attendance.action_sync_attendance_wizard
#: model:ir.ui.menu,name:fingerprint_attendance.menu_sync_attendance_wizard
msgid "Manual Sync"
msgstr "مزامنة يدوية"

#. module: fingerprint_attendance
#: model:ir.ui.menu,name:fingerprint_attendance.menu_fingerprint_attendance_root
msgid "Fingerprint Attendance"
msgstr "حضور البصمة"

#. module: fingerprint_attendance
#: model:res.groups,name:fingerprint_attendance.group_fingerprint_user
msgid "Fingerprint User"
msgstr "مستخدم البصمة"

#. module: fingerprint_attendance
#: model:res.groups,name:fingerprint_attendance.group_fingerprint_manager
msgid "Fingerprint Manager"
msgstr "مدير البصمة"

#. module: fingerprint_attendance
#: code:addons/fingerprint_attendance/models/fingerprint_device_config.py:0
#, python-format
msgid "Please enter a valid IP address"
msgstr "يرجى إدخال عنوان IP صحيح"

#. module: fingerprint_attendance
#: code:addons/fingerprint_attendance/models/fingerprint_device_config.py:0
#, python-format
msgid "Port number must be between 1 and 65535"
msgstr "رقم المنفذ يجب أن يكون بين 1 و 65535"

#. module: fingerprint_attendance
#: code:addons/fingerprint_attendance/models/fingerprint_device_config.py:0
#, python-format
msgid "Sync interval must be at least 1 minute"
msgstr "فترة المزامنة يجب أن تكون دقيقة واحدة على الأقل"

#. module: fingerprint_attendance
#: code:addons/fingerprint_attendance/models/fingerprint_device_config.py:0
#, python-format
msgid "Connection test successful"
msgstr "اختبار الاتصال نجح"

#. module: fingerprint_attendance
#: code:addons/fingerprint_attendance/models/fingerprint_device_config.py:0
#, python-format
msgid "Connection test failed: %s"
msgstr "فشل اختبار الاتصال: %s"

#. module: fingerprint_attendance
#: code:addons/fingerprint_attendance/models/fingerprint_device_config.py:0
#, python-format
msgid "Sync completed. Processed: %d, Errors: %d"
msgstr "اكتملت المزامنة. تم معالجة: %d، أخطاء: %d"

#. module: fingerprint_attendance
#: code:addons/fingerprint_attendance/models/fingerprint_device_config.py:0
#, python-format
msgid "Sync failed: %s"
msgstr "فشلت المزامنة: %s"
