# دليل التثبيت التفصيلي | Detailed Installation Guide

## المتطلبات الأساسية | Prerequisites

### 1. متطلبات النظام | System Requirements
- **Odoo**: Version 15 Community Edition
- **Python**: 3.7 or higher
- **Operating System**: Linux (Ubuntu/CentOS) or Windows
- **RAM**: Minimum 2GB (4GB recommended)
- **Storage**: 100MB for module files
- **Network**: LAN connectivity to fingerprint devices

### 2. متطلبات الشبكة | Network Requirements
- **Device IP**: Static IP address for fingerprint device
- **Port**: Usually 4370 (configurable)
- **Protocol**: TCP/IP
- **Firewall**: Allow communication on device port

## خطوات التثبيت | Installation Steps

### الخطوة 1: تثبيت المكتبات المطلوبة | Step 1: Install Required Libraries

#### على Linux | On Linux:
```bash
# Update package manager
sudo apt update  # For Ubuntu/Debian
sudo yum update  # For CentOS/RHEL

# Install pip if not available
sudo apt install python3-pip  # Ubuntu/Debian
sudo yum install python3-pip  # CentOS/RHEL

# Install pyzk library
pip3 install pyzk

# Verify installation
python3 -c "import zk; print('pyzk installed successfully')"
```

#### على Windows | On Windows:
```cmd
# Install pyzk library
pip install pyzk

# Verify installation
python -c "import zk; print('pyzk installed successfully')"
```

### الخطوة 2: تحضير الموديول | Step 2: Prepare Module

```bash
# Navigate to Odoo addons directory
cd /path/to/odoo/addons

# Copy the module (if downloaded separately)
cp -r /path/to/fingerprint_attendance ./

# Set proper permissions (Linux only)
sudo chown -R odoo:odoo fingerprint_attendance
sudo chmod -R 755 fingerprint_attendance
```

### الخطوة 3: تثبيت الموديول في أودوو | Step 3: Install Module in Odoo

1. **إعادة تشغيل خدمة أودوو | Restart Odoo Service**
   ```bash
   # Linux
   sudo systemctl restart odoo
   
   # Or if using manual startup
   python3 odoo-bin -c /path/to/odoo.conf
   ```

2. **تحديث قائمة التطبيقات | Update Apps List**
   - Login to Odoo as administrator
   - Go to Apps menu
   - Click "Update Apps List"
   - Wait for the update to complete

3. **البحث عن الموديول | Search for Module**
   - In Apps menu, search for "Fingerprint"
   - Find "Fingerprint Attendance Integration"
   - Click "Install"

### الخطوة 4: التحقق من التثبيت | Step 4: Verify Installation

1. **فحص القوائم | Check Menus**
   - Go to HR menu
   - Look for "Configuration" submenu
   - Verify "Fingerprint Attendance" section exists

2. **فحص الصلاحيات | Check Permissions**
   - Go to Settings > Users & Companies > Groups
   - Verify "Fingerprint User" and "Fingerprint Manager" groups exist

## الإعداد الأولي | Initial Configuration

### 1. إعداد المستخدمين | User Setup

```sql
-- Add users to fingerprint groups (if needed via SQL)
INSERT INTO res_groups_users_rel (gid, uid) 
SELECT g.id, u.id 
FROM res_groups g, res_users u 
WHERE g.name = 'Fingerprint Manager' 
AND u.login = 'admin';
```

### 2. إعداد الجهاز الأول | First Device Setup

1. **الانتقال لإعدادات الجهاز | Navigate to Device Settings**
   - HR > Configuration > Fingerprint Attendance > Fingerprint Devices
   - Click "Create"

2. **ملء بيانات الجهاز | Fill Device Information**
   ```
   Device Name: Main Entrance Device
   Device IP Address: *************
   Port: 4370
   Sync Interval: 5 (minutes)
   Enable Auto Sync: ✓
   ```

3. **اختبار الاتصال | Test Connection**
   - Click "Test Connection"
   - Verify success message

### 3. إعداد الموظفين | Employee Setup

1. **ربط الموظفين | Link Employees**
   - Go to HR > Employees
   - Open each employee record
   - Go to "Fingerprint Settings" tab
   - Set "Device User ID" (must match device)

## استكشاف أخطاء التثبيت | Installation Troubleshooting

### مشكلة: pyzk غير موجود | Issue: pyzk not found

```bash
# Check Python path
python3 -c "import sys; print(sys.path)"

# Install in specific Python version
python3.8 -m pip install pyzk

# For virtual environments
source /path/to/venv/bin/activate
pip install pyzk
```

### مشكلة: خطأ في الصلاحيات | Issue: Permission Error

```bash
# Fix file permissions
sudo chown -R odoo:odoo /path/to/odoo/addons/fingerprint_attendance
sudo chmod -R 755 /path/to/odoo/addons/fingerprint_attendance

# Check Odoo log for specific errors
tail -f /var/log/odoo/odoo.log
```

### مشكلة: الموديول لا يظهر | Issue: Module Not Appearing

1. **فحص مسار الإضافات | Check Addons Path**
   ```bash
   # Verify addons path in odoo.conf
   grep addons_path /etc/odoo/odoo.conf
   ```

2. **فحص ملف البيان | Check Manifest File**
   ```bash
   # Verify __manifest__.py syntax
   python3 -c "exec(open('/path/to/fingerprint_attendance/__manifest__.py').read())"
   ```

### مشكلة: خطأ في قاعدة البيانات | Issue: Database Error

```sql
-- Check if tables were created
\dt fingerprint*

-- Verify model access rights
SELECT * FROM ir_model WHERE model LIKE 'fingerprint%';
```

## اختبار التثبيت | Installation Testing

### 1. اختبار الوظائف الأساسية | Basic Function Test

```python
# Test in Odoo shell
# Start Odoo shell: python3 odoo-bin shell -c /path/to/odoo.conf -d database_name

# Test model creation
device = env['fingerprint.device.config'].create({
    'name': 'Test Device',
    'device_ip': '*************',
    'device_port': 4370,
})

# Test connection (if device available)
device.test_connection()
```

### 2. اختبار المزامنة | Sync Test

1. **إعداد موظف تجريبي | Setup Test Employee**
   ```python
   employee = env['hr.employee'].create({
       'name': 'Test Employee',
       'device_user_id': '001',
   })
   ```

2. **تشغيل المزامنة | Run Sync**
   ```python
   device.sync_attendance_data()
   ```

## الصيانة | Maintenance

### النسخ الاحتياطي | Backup

```bash
# Backup module data
pg_dump -U odoo_user -t fingerprint_device_config database_name > device_backup.sql
pg_dump -U odoo_user -t fingerprint_sync_log database_name > log_backup.sql
```

### التحديث | Updates

```bash
# Update pyzk library
pip install --upgrade pyzk

# Update module (after code changes)
# In Odoo: Apps > Fingerprint Attendance Integration > Upgrade
```

### المراقبة | Monitoring

```bash
# Monitor sync logs
tail -f /var/log/odoo/odoo.log | grep fingerprint

# Check cron job status
# In Odoo: Settings > Technical > Automation > Scheduled Actions
```

## الدعم | Support

للحصول على مساعدة إضافية:
- راجع ملف README.md
- تحقق من سجلات النظام
- تواصل مع فريق الدعم الفني

For additional help:
- Review README.md file
- Check system logs
- Contact technical support team
