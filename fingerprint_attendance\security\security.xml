<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Security Groups -->
    <record id="group_fingerprint_user" model="res.groups">
        <field name="name">Fingerprint User</field>
        <field name="category_id" ref="base.module_category_human_resources"/>
        <field name="implied_ids" eval="[(4, ref('hr.group_hr_user'))]"/>
        <field name="comment">Can view fingerprint attendance data and sync logs</field>
    </record>

    <record id="group_fingerprint_manager" model="res.groups">
        <field name="name">Fingerprint Manager</field>
        <field name="category_id" ref="base.module_category_human_resources"/>
        <field name="implied_ids" eval="[(4, ref('group_fingerprint_user')), (4, ref('hr.group_hr_manager'))]"/>
        <field name="comment">Can configure fingerprint devices and manage synchronization</field>
    </record>

    <!-- Record Rules -->
    <record id="fingerprint_device_config_rule" model="ir.rule">
        <field name="name">Fingerprint Device Config: Multi-Company</field>
        <field name="model_id" ref="model_fingerprint_device_config"/>
        <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        <field name="groups" eval="[(4, ref('group_fingerprint_user'))]"/>
    </record>

    <record id="fingerprint_sync_log_rule" model="ir.rule">
        <field name="name">Fingerprint Sync Log: Multi-Company</field>
        <field name="model_id" ref="model_fingerprint_sync_log"/>
        <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        <field name="groups" eval="[(4, ref('group_fingerprint_user'))]"/>
    </record>
</odoo>
