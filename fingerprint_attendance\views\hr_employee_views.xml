<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- HR Employee Form View Extension -->
    <record id="view_hr_employee_form_fingerprint" model="ir.ui.view">
        <field name="name">hr.employee.form.fingerprint</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_form"/>
        <field name="arch" type="xml">
            <xpath expr="//page[@name='hr_settings']" position="after">
                <page name="fingerprint_settings" string="Fingerprint Settings">
                    <group>
                        <group name="fingerprint_info" string="Fingerprint Information">
                            <field name="device_user_id" placeholder="e.g., 001, 123"/>
                            <field name="fingerprint_enrolled"/>
                            <field name="last_attendance_sync" readonly="1"/>
                        </group>
                    </group>
                    
                    <div class="oe_button_box" name="fingerprint_buttons">
                        <button name="action_sync_fingerprint_data" type="object" 
                                string="Sync Fingerprint Data" 
                                class="btn-primary" icon="fa-refresh"
                                attrs="{'invisible': [('device_user_id', '=', False)]}"/>
                    </div>
                    
                    <div class="alert alert-info" role="alert" 
                         attrs="{'invisible': [('device_user_id', '!=', False)]}">
                        <strong>Note:</strong> Please set the Device User ID to enable fingerprint synchronization for this employee.
                        This ID should match the user ID configured in your fingerprint device.
                    </div>
                </page>
            </xpath>
        </field>
    </record>

    <!-- HR Employee Tree View Extension -->
    <record id="view_hr_employee_tree_fingerprint" model="ir.ui.view">
        <field name="name">hr.employee.tree.fingerprint</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_tree"/>
        <field name="arch" type="xml">
            <field name="work_phone" position="after">
                <field name="device_user_id" optional="hide"/>
                <field name="fingerprint_enrolled" optional="hide"/>
            </field>
        </field>
    </record>

    <!-- HR Employee Search View Extension -->
    <record id="view_hr_employee_search_fingerprint" model="ir.ui.view">
        <field name="name">hr.employee.search.fingerprint</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_filter"/>
        <field name="arch" type="xml">
            <field name="name" position="after">
                <field name="device_user_id"/>
            </field>
            <filter name="inactive" position="after">
                <separator/>
                <filter name="fingerprint_enrolled" string="Fingerprint Enrolled" 
                        domain="[('fingerprint_enrolled', '=', True)]"/>
                <filter name="fingerprint_not_enrolled" string="Fingerprint Not Enrolled" 
                        domain="[('fingerprint_enrolled', '=', False)]"/>
                <filter name="has_device_id" string="Has Device User ID" 
                        domain="[('device_user_id', '!=', False)]"/>
                <filter name="no_device_id" string="No Device User ID" 
                        domain="[('device_user_id', '=', False)]"/>
            </filter>
        </field>
    </record>
</odoo>
