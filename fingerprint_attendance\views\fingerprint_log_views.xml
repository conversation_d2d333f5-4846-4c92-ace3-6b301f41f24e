<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Fingerprint Sync Log Form View -->
    <record id="view_fingerprint_sync_log_form" model="ir.ui.view">
        <field name="name">fingerprint.sync.log.form</field>
        <field name="model">fingerprint.sync.log</field>
        <field name="arch" type="xml">
            <form string="Sync Log" create="false" edit="false" delete="false">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="display_name"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group name="basic_info" string="Basic Information">
                            <field name="device_config_id"/>
                            <field name="sync_date"/>
                            <field name="status" widget="badge" 
                                   decoration-success="status == 'success'"
                                   decoration-warning="status == 'warning'"
                                   decoration-danger="status == 'error'"/>
                            <field name="company_id" groups="base.group_multi_company"/>
                        </group>
                    </group>
                    
                    <group name="message_details" string="Message">
                        <field name="message" nolabel="1"/>
                    </group>
                    
                    <group name="technical_details" string="Technical Details" 
                           attrs="{'invisible': [('details', '=', False)]}">
                        <field name="details" nolabel="1"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Fingerprint Sync Log Tree View -->
    <record id="view_fingerprint_sync_log_tree" model="ir.ui.view">
        <field name="name">fingerprint.sync.log.tree</field>
        <field name="model">fingerprint.sync.log</field>
        <field name="arch" type="xml">
            <tree string="Sync Logs" create="false" edit="false" 
                  decoration-success="status == 'success'"
                  decoration-warning="status == 'warning'"
                  decoration-danger="status == 'error'">
                <field name="sync_date"/>
                <field name="device_config_id"/>
                <field name="status" widget="badge"/>
                <field name="message"/>
                <field name="company_id" groups="base.group_multi_company"/>
            </tree>
        </field>
    </record>

    <!-- Fingerprint Sync Log Search View -->
    <record id="view_fingerprint_sync_log_search" model="ir.ui.view">
        <field name="name">fingerprint.sync.log.search</field>
        <field name="model">fingerprint.sync.log</field>
        <field name="arch" type="xml">
            <search string="Search Sync Logs">
                <field name="device_config_id"/>
                <field name="message"/>
                <separator/>
                <filter name="filter_device_config_id" string="Current Device" domain="[]" context="{'search_default_device_config_id': 1}"/>
                <separator/>
                <filter name="success" string="Success" domain="[('status', '=', 'success')]"/>
                <filter name="warning" string="Warning" domain="[('status', '=', 'warning')]"/>
                <filter name="error" string="Error" domain="[('status', '=', 'error')]"/>
                <separator/>
                <filter name="today" string="Today" 
                        domain="[('sync_date', '&gt;=', datetime.datetime.combine(context_today(), datetime.time(0,0,0))),
                                 ('sync_date', '&lt;=', datetime.datetime.combine(context_today(), datetime.time(23,59,59)))]"/>
                <filter name="this_week" string="This Week" 
                        domain="[('sync_date', '&gt;=', (context_today() - datetime.timedelta(days=context_today().weekday())).strftime('%Y-%m-%d')),
                                 ('sync_date', '&lt;=', (context_today() + datetime.timedelta(days=6-context_today().weekday())).strftime('%Y-%m-%d'))]"/>
                <filter name="this_month" string="This Month" 
                        domain="[('sync_date', '&gt;=', context_today().strftime('%Y-%m-01')),
                                 ('sync_date', '&lt;=', (context_today().replace(day=1) + datetime.timedelta(days=32)).replace(day=1) - datetime.timedelta(days=1))]"/>
                <separator/>
                <group expand="0" string="Group By">
                    <filter name="group_by_device" string="Device" domain="[]" context="{'group_by': 'device_config_id'}"/>
                    <filter name="group_by_status" string="Status" domain="[]" context="{'group_by': 'status'}"/>
                    <filter name="group_by_date" string="Date" domain="[]" context="{'group_by': 'sync_date:day'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Fingerprint Sync Log Action -->
    <record id="action_fingerprint_sync_log" model="ir.actions.act_window">
        <field name="name">Sync Logs</field>
        <field name="res_model">fingerprint.sync.log</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_today': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No sync logs found!
            </p>
            <p>
                Sync logs will appear here after fingerprint device synchronization.
                Use the sync button on device configuration to start synchronization.
            </p>
        </field>
    </record>
</odoo>
