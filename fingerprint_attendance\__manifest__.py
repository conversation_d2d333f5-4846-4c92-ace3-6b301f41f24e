# -*- coding: utf-8 -*-
{
    'name': 'Fingerprint Attendance Integration',
    'version': '********.0',
    'category': 'Human Resources',
    'summary': 'Integration with fingerprint attendance devices for automatic attendance tracking',
    'description': """
Fingerprint Attendance Integration
==================================

This module provides integration with fingerprint attendance devices to automatically
sync attendance data with Odoo HR attendance system.

Features:
---------
* Connect to fingerprint devices via TCP/IP
* Support for ZKTeco and other common attendance devices
* Automatic synchronization of attendance data
* Manual sync option
* Configuration interface for device settings
* Activity logging for all operations
* Multi-language support (Arabic/English)
* Real-time attendance tracking

Requirements:
-------------
* Odoo 15 Community Edition
* Network access to fingerprint device
* Python pyzk library for ZKTeco devices

Installation:
-------------
1. Install required Python dependencies
2. Copy module to addons directory
3. Update apps list
4. Install the module
5. Configure device settings

Configuration:
--------------
Go to HR > Configuration > Fingerprint Device Settings to configure:
* Device IP address
* Port number
* Username/Password (if required)
* Sync interval
* Enable/disable automatic sync
    """,
    'author': 'Your Company',
    'website': 'https://www.yourcompany.com',
    'license': 'LGPL-3',
    'depends': [
        'base',
        'hr',
        'hr_attendance',
    ],
    'external_dependencies': {
        'python': ['pyzk'],
    },
    'data': [
        'security/security.xml',
        'security/ir.model.access.csv',
        'data/ir_cron_data.xml',
        'views/fingerprint_device_views.xml',
        'views/fingerprint_log_views.xml',
        'views/hr_employee_views.xml',
        'views/menu_views.xml',
        'wizard/sync_attendance_wizard_views.xml',
    ],
    'demo': [],
    'installable': True,
    'auto_install': False,
    'application': True,
    'sequence': 100,
    'post_init_hook': 'post_init_hook',
    'uninstall_hook': 'uninstall_hook',
}
