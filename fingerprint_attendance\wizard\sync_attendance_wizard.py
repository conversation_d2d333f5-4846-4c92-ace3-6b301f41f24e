# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError


class SyncAttendanceWizard(models.TransientModel):
    _name = 'sync.attendance.wizard'
    _description = 'Manual Attendance Synchronization Wizard'

    device_ids = fields.Many2many(
        'fingerprint.device.config',
        string='Devices to Sync',
        domain=[('active', '=', True)],
        help='Select devices to synchronize attendance data from'
    )
    sync_all_devices = fields.Boolean(
        string='Sync All Active Devices',
        default=True,
        help='Synchronize from all active devices'
    )
    employee_ids = fields.Many2many(
        'hr.employee',
        string='Specific Employees',
        domain=[('device_user_id', '!=', False)],
        help='Leave empty to sync all employees, or select specific employees'
    )
    force_sync = fields.Bo<PERSON>an(
        string='Force Sync',
        default=False,
        help='Force synchronization even if there are recent sync records'
    )
    
    @api.onchange('sync_all_devices')
    def _onchange_sync_all_devices(self):
        """Update device selection based on sync_all_devices"""
        if self.sync_all_devices:
            self.device_ids = self.env['fingerprint.device.config'].search([('active', '=', True)])
        else:
            self.device_ids = False

    def action_sync_attendance(self):
        """Execute attendance synchronization"""
        # Determine devices to sync
        if self.sync_all_devices:
            devices = self.env['fingerprint.device.config'].search([('active', '=', True)])
        else:
            devices = self.device_ids
        
        if not devices:
            raise UserError(_('No devices selected for synchronization'))
        
        # Track sync results
        sync_results = []
        total_processed = 0
        total_errors = 0
        
        for device in devices:
            try:
                # Perform sync for this device
                result = device.sync_attendance_data()
                sync_results.append({
                    'device': device.name,
                    'status': 'success',
                    'message': _('Sync completed successfully')
                })
                total_processed += 1
                
            except Exception as e:
                error_msg = str(e)
                sync_results.append({
                    'device': device.name,
                    'status': 'error',
                    'message': error_msg
                })
                total_errors += 1
        
        # Prepare result message
        if total_errors == 0:
            title = _('Sync Completed Successfully')
            message = _('All %d devices synchronized successfully') % total_processed
            notification_type = 'success'
        elif total_processed == 0:
            title = _('Sync Failed')
            message = _('All %d devices failed to synchronize') % total_errors
            notification_type = 'danger'
        else:
            title = _('Sync Completed with Errors')
            message = _('%d devices synchronized successfully, %d failed') % (total_processed, total_errors)
            notification_type = 'warning'
        
        # Show detailed results in a new wizard
        result_wizard = self.env['sync.attendance.result.wizard'].create({
            'sync_results': '\n'.join([
                _('%s: %s - %s') % (r['device'], r['status'].upper(), r['message'])
                for r in sync_results
            ]),
            'total_devices': len(devices),
            'successful_syncs': total_processed,
            'failed_syncs': total_errors,
        })
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Sync Results'),
            'res_model': 'sync.attendance.result.wizard',
            'res_id': result_wizard.id,
            'view_mode': 'form',
            'target': 'new',
        }


class SyncAttendanceResultWizard(models.TransientModel):
    _name = 'sync.attendance.result.wizard'
    _description = 'Attendance Synchronization Results'

    sync_results = fields.Text(
        string='Sync Results',
        readonly=True,
        help='Detailed results of the synchronization process'
    )
    total_devices = fields.Integer(
        string='Total Devices',
        readonly=True
    )
    successful_syncs = fields.Integer(
        string='Successful Syncs',
        readonly=True
    )
    failed_syncs = fields.Integer(
        string='Failed Syncs',
        readonly=True
    )
    
    def action_view_sync_logs(self):
        """View sync logs"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Sync Logs'),
            'res_model': 'fingerprint.sync.log',
            'view_mode': 'tree,form',
            'domain': [('sync_date', '>=', fields.Datetime.now().replace(hour=0, minute=0, second=0))],
            'context': {'search_default_today': 1},
        }
    
    def action_close(self):
        """Close the wizard"""
        return {'type': 'ir.actions.act_window_close'}
