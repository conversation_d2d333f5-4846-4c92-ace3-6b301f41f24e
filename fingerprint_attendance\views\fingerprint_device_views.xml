<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Fingerprint Device Configuration Form View -->
    <record id="view_fingerprint_device_config_form" model="ir.ui.view">
        <field name="name">fingerprint.device.config.form</field>
        <field name="model">fingerprint.device.config</field>
        <field name="arch" type="xml">
            <form string="Fingerprint Device Configuration">
                <header>
                    <button name="test_connection" type="object" string="Test Connection" 
                            class="btn-primary" icon="fa-plug"/>
                    <button name="sync_attendance_data" type="object" string="Sync Now" 
                            class="btn-secondary" icon="fa-refresh"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_sync_logs" type="object"
                                class="oe_stat_button" icon="fa-list-alt">
                            <field name="total_syncs" widget="statinfo" string="Total Syncs"/>
                        </button>
                    </div>
                    
                    <widget name="web_ribbon" title="Archived" bg_color="bg-danger" 
                            attrs="{'invisible': [('active', '=', True)]}"/>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Device Name"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group name="connection_settings" string="Connection Settings">
                            <field name="device_ip" placeholder="*************"/>
                            <field name="device_port"/>
                            <field name="timeout"/>
                            <field name="username"/>
                            <field name="password" password="True"/>
                        </group>
                        <group name="sync_settings" string="Synchronization Settings">
                            <field name="auto_sync"/>
                            <field name="sync_interval" attrs="{'invisible': [('auto_sync', '=', False)]}"/>
                            <field name="last_sync_date" readonly="1"/>
                            <field name="active"/>
                            <field name="company_id" groups="base.group_multi_company"/>
                        </group>
                    </group>
                    
                    <group name="status_info" string="Status Information" attrs="{'invisible': [('last_sync_date', '=', False)]}">
                        <field name="last_error" readonly="1" attrs="{'invisible': [('last_error', '=', False)]}"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Fingerprint Device Configuration Tree View -->
    <record id="view_fingerprint_device_config_tree" model="ir.ui.view">
        <field name="name">fingerprint.device.config.tree</field>
        <field name="model">fingerprint.device.config</field>
        <field name="arch" type="xml">
            <tree string="Fingerprint Devices" decoration-muted="not active">
                <field name="name"/>
                <field name="device_ip"/>
                <field name="device_port"/>
                <field name="auto_sync"/>
                <field name="last_sync_date"/>
                <field name="total_syncs"/>
                <field name="active"/>
                <field name="company_id" groups="base.group_multi_company"/>
            </tree>
        </field>
    </record>

    <!-- Fingerprint Device Configuration Search View -->
    <record id="view_fingerprint_device_config_search" model="ir.ui.view">
        <field name="name">fingerprint.device.config.search</field>
        <field name="model">fingerprint.device.config</field>
        <field name="arch" type="xml">
            <search string="Search Fingerprint Devices">
                <field name="name"/>
                <field name="device_ip"/>
                <separator/>
                <filter name="active" string="Active" domain="[('active', '=', True)]"/>
                <filter name="inactive" string="Inactive" domain="[('active', '=', False)]"/>
                <separator/>
                <filter name="auto_sync" string="Auto Sync Enabled" domain="[('auto_sync', '=', True)]"/>
                <filter name="manual_sync" string="Manual Sync Only" domain="[('auto_sync', '=', False)]"/>
                <separator/>
                <group expand="0" string="Group By">
                    <filter name="group_by_company" string="Company" domain="[]" context="{'group_by': 'company_id'}"/>
                    <filter name="group_by_auto_sync" string="Auto Sync" domain="[]" context="{'group_by': 'auto_sync'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Fingerprint Device Configuration Action -->
    <record id="action_fingerprint_device_config" model="ir.actions.act_window">
        <field name="name">Fingerprint Devices</field>
        <field name="res_model">fingerprint.device.config</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first fingerprint device configuration!
            </p>
            <p>
                Configure your fingerprint attendance devices to automatically sync
                attendance data with Odoo HR system.
            </p>
        </field>
    </record>
</odoo>
