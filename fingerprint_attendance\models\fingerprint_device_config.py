# -*- coding: utf-8 -*-

import logging
from datetime import datetime, timedelta
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError

_logger = logging.getLogger(__name__)

try:
    from zk import ZK
    ZK_AVAILABLE = True
except ImportError:
    ZK_AVAILABLE = False
    _logger.warning("pyzk library not found. Please install it using: pip install pyzk")


class FingerprintDeviceConfig(models.Model):
    _name = 'fingerprint.device.config'
    _description = 'Fingerprint Device Configuration'
    _rec_name = 'name'

    name = fields.Char(
        string='Device Name',
        required=True,
        help='Name to identify the fingerprint device'
    )
    device_ip = fields.Char(
        string='Device IP Address',
        required=True,
        help='IP address of the fingerprint device'
    )
    device_port = fields.Integer(
        string='Port',
        default=4370,
        required=True,
        help='Port number for device connection'
    )
    username = fields.Char(
        string='Username',
        help='Username for device authentication (if required)'
    )
    password = fields.Char(
        string='Password',
        help='Password for device authentication (if required)'
    )
    timeout = fields.Integer(
        string='Connection Timeout',
        default=60,
        help='Connection timeout in seconds'
    )
    sync_interval = fields.Integer(
        string='Sync Interval (minutes)',
        default=5,
        required=True,
        help='Automatic synchronization interval in minutes'
    )
    auto_sync = fields.Boolean(
        string='Enable Auto Sync',
        default=True,
        help='Enable automatic synchronization'
    )
    last_sync_date = fields.Datetime(
        string='Last Sync Date',
        readonly=True,
        help='Last successful synchronization date'
    )
    active = fields.Boolean(
        string='Active',
        default=True,
        help='Enable/disable this device configuration'
    )
    company_id = fields.Many2one(
        'res.company',
        string='Company',
        default=lambda self: self.env.company,
        required=True
    )
    
    # Statistics fields
    total_syncs = fields.Integer(
        string='Total Syncs',
        readonly=True,
        default=0,
        help='Total number of successful synchronizations'
    )
    last_error = fields.Text(
        string='Last Error',
        readonly=True,
        help='Last synchronization error message'
    )
    
    @api.constrains('device_ip')
    def _check_device_ip(self):
        """Validate IP address format"""
        import re
        ip_pattern = re.compile(r'^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$')
        for record in self:
            if record.device_ip and not ip_pattern.match(record.device_ip):
                raise ValidationError(_('Please enter a valid IP address'))
    
    @api.constrains('device_port')
    def _check_device_port(self):
        """Validate port number"""
        for record in self:
            if record.device_port and (record.device_port < 1 or record.device_port > 65535):
                raise ValidationError(_('Port number must be between 1 and 65535'))
    
    @api.constrains('sync_interval')
    def _check_sync_interval(self):
        """Validate sync interval"""
        for record in self:
            if record.sync_interval and record.sync_interval < 1:
                raise ValidationError(_('Sync interval must be at least 1 minute'))

    def test_connection(self):
        """Test connection to fingerprint device"""
        self.ensure_one()
        
        if not ZK_AVAILABLE:
            raise UserError(_('pyzk library is not installed. Please install it using: pip install pyzk'))
        
        try:
            # Create ZK instance
            zk = ZK(self.device_ip, port=self.device_port, timeout=self.timeout)
            
            # Connect to device
            conn = zk.connect()
            
            if conn:
                # Get device info
                device_info = {
                    'firmware_version': conn.get_firmware_version(),
                    'serialnumber': conn.get_serialnumber(),
                    'platform': conn.get_platform(),
                    'device_name': conn.get_device_name(),
                    'users_count': len(conn.get_users()),
                    'attendance_count': len(conn.get_attendance()),
                }
                
                # Disconnect
                conn.disconnect()
                
                # Log successful connection
                self._create_sync_log('success', _('Connection test successful'), device_info)
                
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Connection Successful'),
                        'message': _('Successfully connected to device: %s') % device_info.get('device_name', 'Unknown'),
                        'type': 'success',
                        'sticky': False,
                    }
                }
            else:
                raise UserError(_('Failed to connect to device'))
                
        except Exception as e:
            error_msg = str(e)
            self._create_sync_log('error', _('Connection test failed: %s') % error_msg)
            raise UserError(_('Connection failed: %s') % error_msg)

    def sync_attendance_data(self):
        """Synchronize attendance data from fingerprint device"""
        self.ensure_one()
        
        if not ZK_AVAILABLE:
            raise UserError(_('pyzk library is not installed. Please install it using: pip install pyzk'))
        
        try:
            # Create ZK instance
            zk = ZK(self.device_ip, port=self.device_port, timeout=self.timeout)
            
            # Connect to device
            conn = zk.connect()
            
            if not conn:
                raise UserError(_('Failed to connect to device'))
            
            # Get attendance data
            attendances = conn.get_attendance()
            
            # Get users data for mapping
            users = conn.get_users()
            user_mapping = {user.user_id: user for user in users}
            
            # Disconnect from device
            conn.disconnect()
            
            # Process attendance data
            processed_count = 0
            error_count = 0
            
            for attendance in attendances:
                try:
                    self._process_attendance_record(attendance, user_mapping)
                    processed_count += 1
                except Exception as e:
                    error_count += 1
                    _logger.error("Error processing attendance record: %s", str(e))
            
            # Update sync statistics
            self.write({
                'last_sync_date': fields.Datetime.now(),
                'total_syncs': self.total_syncs + 1,
                'last_error': False if error_count == 0 else _('%d records failed to process') % error_count
            })
            
            # Create sync log
            message = _('Sync completed. Processed: %d, Errors: %d') % (processed_count, error_count)
            self._create_sync_log('success' if error_count == 0 else 'warning', message, {
                'total_records': len(attendances),
                'processed': processed_count,
                'errors': error_count
            })
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Sync Completed'),
                    'message': message,
                    'type': 'success' if error_count == 0 else 'warning',
                    'sticky': False,
                }
            }
            
        except Exception as e:
            error_msg = str(e)
            self.write({'last_error': error_msg})
            self._create_sync_log('error', _('Sync failed: %s') % error_msg)
            raise UserError(_('Sync failed: %s') % error_msg)

    def _process_attendance_record(self, attendance, user_mapping):
        """Process a single attendance record"""
        # Get user info
        user_info = user_mapping.get(attendance.user_id)
        if not user_info:
            _logger.warning("User ID %s not found in device users", attendance.user_id)
            return
        
        # Find employee by device user ID
        employee = self.env['hr.employee'].search([
            ('device_user_id', '=', attendance.user_id)
        ], limit=1)
        
        if not employee:
            _logger.warning("Employee not found for device user ID: %s", attendance.user_id)
            return
        
        # Check if attendance record already exists
        existing_attendance = self.env['hr.attendance'].search([
            ('employee_id', '=', employee.id),
            ('check_in', '=', attendance.timestamp),
            ('device_attendance_id', '=', attendance.user_id)
        ], limit=1)
        
        if existing_attendance:
            return  # Skip duplicate records
        
        # Create attendance record
        attendance_vals = {
            'employee_id': employee.id,
            'check_in': attendance.timestamp,
            'device_attendance_id': attendance.user_id,
            'device_name': self.name,
        }
        
        # Determine if this is check-in or check-out based on punch type
        # Punch type: 0=check-in, 1=check-out, 2=break-out, 3=break-in, 4=overtime-in, 5=overtime-out
        if hasattr(attendance, 'punch') and attendance.punch in [1, 2, 5]:  # Check-out types
            # Find the last check-in without check-out for this employee
            last_attendance = self.env['hr.attendance'].search([
                ('employee_id', '=', employee.id),
                ('check_out', '=', False)
            ], order='check_in desc', limit=1)
            
            if last_attendance:
                last_attendance.write({'check_out': attendance.timestamp})
                return
        
        # Create new attendance record
        self.env['hr.attendance'].create(attendance_vals)

    def _create_sync_log(self, status, message, details=None):
        """Create a synchronization log entry"""
        self.env['fingerprint.sync.log'].create({
            'device_config_id': self.id,
            'sync_date': fields.Datetime.now(),
            'status': status,
            'message': message,
            'details': str(details) if details else False,
        })

    def action_view_sync_logs(self):
        """View sync logs for this device"""
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': _('Sync Logs - %s') % self.name,
            'res_model': 'fingerprint.sync.log',
            'view_mode': 'tree,form',
            'domain': [('device_config_id', '=', self.id)],
            'context': {
                'default_device_config_id': self.id,
                'search_default_device_config_id': self.id,
            },
        }

    @api.model
    def cron_sync_attendance(self):
        """Cron job to automatically sync attendance data"""
        active_devices = self.search([
            ('active', '=', True),
            ('auto_sync', '=', True)
        ])
        
        for device in active_devices:
            try:
                device.sync_attendance_data()
            except Exception as e:
                _logger.error("Auto sync failed for device %s: %s", device.name, str(e))
                device._create_sync_log('error', _('Auto sync failed: %s') % str(e))
