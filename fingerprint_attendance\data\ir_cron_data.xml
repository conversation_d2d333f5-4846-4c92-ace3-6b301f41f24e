<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Cron Job for Automatic Attendance Synchronization -->
    <record id="ir_cron_sync_fingerprint_attendance" model="ir.cron">
        <field name="name">Sync Fingerprint Attendance Data</field>
        <field name="model_id" ref="model_fingerprint_device_config"/>
        <field name="state">code</field>
        <field name="code">model.cron_sync_attendance()</field>
        <field name="interval_number">5</field>
        <field name="interval_type">minutes</field>
        <field name="numbercall">-1</field>
        <field name="active">True</field>
        <field name="doall">False</field>
        <field name="user_id" ref="base.user_root"/>
    </record>

    <!-- Cron Job for Cleaning Old Sync Logs -->
    <record id="ir_cron_cleanup_sync_logs" model="ir.cron">
        <field name="name">Cleanup Old Fingerprint Sync Logs</field>
        <field name="model_id" ref="model_fingerprint_sync_log"/>
        <field name="state">code</field>
        <field name="code">model.cleanup_old_logs(days=30)</field>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
        <field name="active">True</field>
        <field name="doall">False</field>
        <field name="user_id" ref="base.user_root"/>
        <field name="nextcall" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d 02:00:00')"/>
    </record>
</odoo>
