# -*- coding: utf-8 -*-

from odoo import models, fields, api, _


class HrAttendance(models.Model):
    _inherit = 'hr.attendance'

    device_attendance_id = fields.Char(
        string='Device Attendance ID',
        help='Attendance record ID from fingerprint device'
    )
    device_name = fields.Char(
        string='Device Name',
        help='Name of the fingerprint device that recorded this attendance'
    )
    is_from_device = fields.Boolean(
        string='From Device',
        compute='_compute_is_from_device',
        store=True,
        help='Indicates if this attendance was recorded by a fingerprint device'
    )
    
    @api.depends('device_attendance_id')
    def _compute_is_from_device(self):
        """Compute if attendance is from device"""
        for record in self:
            record.is_from_device = bool(record.device_attendance_id)

    def name_get(self):
        """Override name_get to show device info"""
        result = []
        for record in self:
            name = super(HrAttendance, record).name_get()[0][1]
            if record.device_name:
                name += _(' (Device: %s)') % record.device_name
            result.append((record.id, name))
        return result
