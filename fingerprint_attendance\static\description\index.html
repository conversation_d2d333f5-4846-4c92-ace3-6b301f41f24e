<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Fingerprint Attendance Integration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 20px;
            color: #333;
        }
        .header {
            text-align: center;
            background: #875A7B;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .feature {
            background: #f4f4f4;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #875A7B;
        }
        .arabic {
            direction: rtl;
            text-align: right;
        }
        .english {
            direction: ltr;
            text-align: left;
        }
        .highlight {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Fingerprint Attendance Integration</h1>
        <h2>تكامل أجهزة البصمة مع أودوو</h2>
        <p>Professional fingerprint device integration for Odoo 15 Community</p>
    </div>

    <div class="arabic">
        <h2>🔍 نظرة عامة</h2>
        <p>موديول متقدم لربط أجهزة البصمة مع نظام أودوو لإدارة الحضور والانصراف تلقائياً</p>
        
        <div class="feature">
            <h3>✨ المميزات الرئيسية</h3>
            <ul>
                <li>اتصال مباشر مع أجهزة البصمة عبر TCP/IP</li>
                <li>مزامنة تلقائية للبيانات كل فترة زمنية محددة</li>
                <li>دعم أجهزة ZKTeco وأجهزة أخرى متوافقة</li>
                <li>واجهة إعدادات شاملة وسهلة الاستخدام</li>
                <li>سجل مفصل لجميع العمليات والأخطاء</li>
                <li>دعم متعدد الشركات والأجهزة</li>
                <li>نظام صلاحيات متقدم</li>
                <li>دعم الترجمة العربية والإنجليزية</li>
            </ul>
        </div>

        <div class="highlight">
            <h3>🎯 مصمم خصيصاً للبيئات المحلية</h3>
            <p>يعمل على الشبكة المحلية (LAN) بدون الحاجة لاتصال بالإنترنت</p>
        </div>
    </div>

    <div class="english">
        <h2>🔍 Overview</h2>
        <p>Advanced module for integrating fingerprint devices with Odoo for automatic attendance management</p>
        
        <div class="feature">
            <h3>✨ Key Features</h3>
            <ul>
                <li>Direct TCP/IP connection to fingerprint devices</li>
                <li>Automatic data synchronization at configurable intervals</li>
                <li>Support for ZKTeco and other compatible devices</li>
                <li>Comprehensive and user-friendly configuration interface</li>
                <li>Detailed logging of all operations and errors</li>
                <li>Multi-company and multi-device support</li>
                <li>Advanced permission system</li>
                <li>Arabic and English translation support</li>
            </ul>
        </div>

        <div class="highlight">
            <h3>🎯 Designed for Local Environments</h3>
            <p>Works on Local Area Network (LAN) without requiring internet connectivity</p>
        </div>
    </div>

    <div class="warning arabic">
        <h3>⚠️ متطلبات التثبيت</h3>
        <p>يجب تثبيت مكتبة pyzk قبل تثبيت الموديول:</p>
        <code>pip install pyzk</code>
    </div>

    <div class="warning english">
        <h3>⚠️ Installation Requirements</h3>
        <p>Install pyzk library before installing the module:</p>
        <code>pip install pyzk</code>
    </div>

    <div class="feature">
        <h3>📋 Supported Devices | الأجهزة المدعومة</h3>
        <ul>
            <li>ZKTeco attendance devices</li>
            <li>TCP/IP enabled fingerprint devices</li>
            <li>Standard attendance protocol devices</li>
        </ul>
    </div>

    <div class="feature">
        <h3>🛠️ Technical Specifications | المواصفات التقنية</h3>
        <ul>
            <li>Odoo 15 Community Edition</li>
            <li>Python 3.7+</li>
            <li>pyzk library</li>
            <li>Network connectivity to devices</li>
        </ul>
    </div>

    <div class="highlight">
        <h3>📞 Support | الدعم الفني</h3>
        <p class="english">For technical support and customization, please contact our team.</p>
        <p class="arabic">للدعم الفني والتخصيص، يرجى التواصل مع فريقنا.</p>
    </div>
</body>
</html>
