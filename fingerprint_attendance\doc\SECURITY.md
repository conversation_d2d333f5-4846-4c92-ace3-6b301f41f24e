# دليل الأمان | Security Guide

## نظرة عامة على الأمان | Security Overview

هذا الموديول يتضمن عدة طبقات أمان لحماية بيانات الحضور والانصراف.

This module includes multiple security layers to protect attendance data.

## مجموعات المستخدمين | User Groups

### 1. Fingerprint User (مستخدم البصمة)
**الصلاحيات | Permissions:**
- عرض إعدادات الأجهزة (قراءة فقط)
- عرض سجلات المزامنة (قراءة فقط)
- تشغيل معالج المزامنة اليدوية
- عرض بيانات الحضور

**الاستخدام المناسب | Appropriate Use:**
- موظفي الموارد البشرية
- المشرفين على الحضور
- المستخدمين الذين يحتاجون لعرض البيانات فقط

### 2. Fingerprint Manager (مدير البصمة)
**الصلاحيات | Permissions:**
- جميع صلاحيات Fingerprint User
- إنشاء وتعديل إعدادات الأجهزة
- حذف سجلات المزامنة
- إدارة إعدادات المزامنة التلقائية
- اختبار اتصال الأجهزة

**الاستخدام المناسب | Appropriate Use:**
- مديري تقنية المعلومات
- مديري الموارد البشرية
- المسؤولين عن النظام

## قواعد الأمان | Security Rules

### 1. قاعدة الشركات المتعددة | Multi-Company Rule
```xml
<record id="fingerprint_device_config_rule" model="ir.rule">
    <field name="name">Fingerprint Device Config: Multi-Company</field>
    <field name="model_id" ref="model_fingerprint_device_config"/>
    <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
</record>
```

**الغرض | Purpose:**
- يضمن أن المستخدمين يرون فقط أجهزة شركتهم
- يمنع الوصول غير المصرح به لبيانات الشركات الأخرى

### 2. قاعدة سجلات المزامنة | Sync Log Rule
```xml
<record id="fingerprint_sync_log_rule" model="ir.rule">
    <field name="name">Fingerprint Sync Log: Multi-Company</field>
    <field name="model_id" ref="model_fingerprint_sync_log"/>
    <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
</record>
```

## أفضل الممارسات الأمنية | Security Best Practices

### 1. إدارة كلمات المرور | Password Management

#### لأجهزة البصمة | For Fingerprint Devices:
- استخدم كلمات مرور قوية للأجهزة
- غيّر كلمات المرور الافتراضية
- قم بتحديث كلمات المرور دورياً

```python
# مثال على إعداد كلمة مرور قوية
device_config = {
    'username': 'admin',
    'password': 'StrongP@ssw0rd123!',  # كلمة مرور قوية
}
```

### 2. أمان الشبكة | Network Security

#### إعدادات الشبكة الآمنة | Secure Network Settings:
```bash
# تقييد الوصول للجهاز عبر الجدار الناري
sudo ufw allow from 192.168.1.0/24 to any port 4370

# منع الوصول من الإنترنت
sudo ufw deny from any to any port 4370
```

#### مراقبة حركة الشبكة | Network Traffic Monitoring:
```bash
# مراقبة الاتصالات بالجهاز
sudo netstat -an | grep :4370

# مراقبة حركة البيانات
sudo tcpdump -i eth0 port 4370
```

### 3. تشفير البيانات | Data Encryption

#### في قاعدة البيانات | In Database:
- استخدم تشفير قاعدة البيانات
- قم بتشفير النسخ الاحتياطية
- استخدم اتصالات SSL/TLS

```bash
# تفعيل SSL في PostgreSQL
# في ملف postgresql.conf
ssl = on
ssl_cert_file = 'server.crt'
ssl_key_file = 'server.key'
```

### 4. مراجعة السجلات | Audit Logging

#### تفعيل سجلات المراجعة | Enable Audit Logs:
```python
# في إعدادات أودوو
'log_level': 'info',
'log_handler': [':INFO'],
'logfile': '/var/log/odoo/odoo.log',
```

#### مراقبة العمليات الحساسة | Monitor Sensitive Operations:
```python
# مثال على تسجيل العمليات
import logging
_logger = logging.getLogger(__name__)

def sync_attendance_data(self):
    _logger.info("Attendance sync started by user: %s", self.env.user.name)
    # ... كود المزامنة
    _logger.info("Attendance sync completed successfully")
```

## التحكم في الوصول | Access Control

### 1. تقييد الوصول للواجهات | Restrict View Access

```xml
<!-- إخفاء القوائم الحساسة -->
<menuitem id="menu_fingerprint_device_config"
          groups="group_fingerprint_manager"/>

<menuitem id="menu_fingerprint_sync_log"
          groups="group_fingerprint_user"/>
```

### 2. تقييد العمليات | Restrict Operations

```python
# في النماذج
@api.model
def create(self, vals):
    # التحقق من الصلاحيات قبل الإنشاء
    if not self.env.user.has_group('fingerprint_attendance.group_fingerprint_manager'):
        raise AccessError(_('Only fingerprint managers can create device configurations'))
    return super().create(vals)
```

### 3. تشفير البيانات الحساسة | Encrypt Sensitive Data

```python
# تشفير كلمات المرور
from cryptography.fernet import Fernet

class FingerprintDeviceConfig(models.Model):
    _name = 'fingerprint.device.config'
    
    password = fields.Char(string='Password')
    
    def _encrypt_password(self, password):
        """تشفير كلمة المرور"""
        key = Fernet.generate_key()
        f = Fernet(key)
        encrypted_password = f.encrypt(password.encode())
        return encrypted_password
```

## مراقبة الأمان | Security Monitoring

### 1. مراقبة محاولات الوصول | Monitor Access Attempts

```python
# تسجيل محاولات الوصول الفاشلة
def test_connection(self):
    try:
        # محاولة الاتصال
        conn = zk.connect()
        _logger.info("Successful connection to device %s by user %s", 
                    self.name, self.env.user.name)
    except Exception as e:
        _logger.warning("Failed connection attempt to device %s by user %s: %s", 
                       self.name, self.env.user.name, str(e))
        raise
```

### 2. تنبيهات الأمان | Security Alerts

```python
# إرسال تنبيهات عند العمليات الحساسة
def sync_attendance_data(self):
    # إرسال إشعار للمديرين
    managers = self.env['res.users'].search([
        ('groups_id', 'in', self.env.ref('fingerprint_attendance.group_fingerprint_manager').id)
    ])
    
    for manager in managers:
        # إرسال إشعار
        self.env['mail.message'].create({
            'subject': 'Attendance Sync Performed',
            'body': f'Attendance sync was performed on device {self.name}',
            'partner_ids': [(4, manager.partner_id.id)],
        })
```

## النسخ الاحتياطي الآمن | Secure Backup

### 1. تشفير النسخ الاحتياطية | Encrypt Backups

```bash
# إنشاء نسخة احتياطية مشفرة
pg_dump -U odoo_user database_name | gpg --cipher-algo AES256 --compress-algo 1 --symmetric --output backup.sql.gpg

# استعادة النسخة الاحتياطية
gpg --decrypt backup.sql.gpg | psql -U odoo_user database_name
```

### 2. تخزين آمن | Secure Storage

```bash
# نقل النسخ الاحتياطية لموقع آمن
rsync -avz --delete /path/to/backups/ user@backup-server:/secure/backups/

# تعيين صلاحيات محدودة
chmod 600 /path/to/backups/*
chown odoo:odoo /path/to/backups/*
```

## التحديثات الأمنية | Security Updates

### 1. تحديث المكتبات | Update Libraries

```bash
# تحديث pyzk لآخر إصدار
pip install --upgrade pyzk

# فحص الثغرات الأمنية
pip audit
```

### 2. مراجعة الكود | Code Review

```python
# فحص الكود للثغرات الأمنية
# استخدام أدوات مثل bandit
pip install bandit
bandit -r /path/to/fingerprint_attendance/
```

## الامتثال والمعايير | Compliance & Standards

### 1. حماية البيانات الشخصية | Personal Data Protection
- تطبيق مبادئ GDPR
- تشفير البيانات الحساسة
- حق المستخدم في حذف البيانات

### 2. معايير الأمان | Security Standards
- اتباع معايير ISO 27001
- تطبيق مبادئ الأمان بالتصميم
- مراجعة دورية للأمان

## الاستجابة للحوادث | Incident Response

### 1. خطة الاستجابة | Response Plan
1. اكتشاف الحادث
2. تقييم الأثر
3. احتواء الحادث
4. التحقيق والتحليل
5. الاستعادة
6. الدروس المستفادة

### 2. جهات الاتصال | Contact Information
- فريق الأمان الداخلي
- مزود الخدمة التقنية
- السلطات المختصة (عند الحاجة)

---

**تذكير مهم**: الأمان مسؤولية مشتركة تتطلب تعاون جميع المستخدمين والإدارة.

**Important Reminder**: Security is a shared responsibility requiring cooperation from all users and management.
