# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase
from odoo.exceptions import ValidationError, UserError
from unittest.mock import patch, MagicMock


class TestFingerprintDeviceConfig(TransactionCase):
    
    def setUp(self):
        super().setUp()
        self.device_config = self.env['fingerprint.device.config'].create({
            'name': 'Test Device',
            'device_ip': '*************',
            'device_port': 4370,
            'sync_interval': 5,
            'auto_sync': True,
        })
    
    def test_device_creation(self):
        """Test device configuration creation"""
        self.assertEqual(self.device_config.name, 'Test Device')
        self.assertEqual(self.device_config.device_ip, '*************')
        self.assertEqual(self.device_config.device_port, 4370)
        self.assertTrue(self.device_config.active)
    
    def test_ip_validation(self):
        """Test IP address validation"""
        with self.assertRaises(ValidationError):
            self.env['fingerprint.device.config'].create({
                'name': 'Invalid IP Device',
                'device_ip': '999.999.999.999',
                'device_port': 4370,
            })
    
    def test_port_validation(self):
        """Test port number validation"""
        with self.assertRaises(ValidationError):
            self.env['fingerprint.device.config'].create({
                'name': 'Invalid Port Device',
                'device_ip': '*************',
                'device_port': 99999,
            })
    
    def test_sync_interval_validation(self):
        """Test sync interval validation"""
        with self.assertRaises(ValidationError):
            self.env['fingerprint.device.config'].create({
                'name': 'Invalid Interval Device',
                'device_ip': '*************',
                'device_port': 4370,
                'sync_interval': 0,
            })
    
    @patch('fingerprint_attendance.models.fingerprint_device_config.ZK_AVAILABLE', True)
    @patch('fingerprint_attendance.models.fingerprint_device_config.ZK')
    def test_connection_success(self, mock_zk_class):
        """Test successful device connection"""
        # Mock ZK instance and connection
        mock_zk = MagicMock()
        mock_conn = MagicMock()
        mock_zk_class.return_value = mock_zk
        mock_zk.connect.return_value = mock_conn
        
        # Mock device info
        mock_conn.get_firmware_version.return_value = '1.0.0'
        mock_conn.get_serialnumber.return_value = '123456'
        mock_conn.get_platform.return_value = 'ZKTeco'
        mock_conn.get_device_name.return_value = 'Test Device'
        mock_conn.get_users.return_value = []
        mock_conn.get_attendance.return_value = []
        
        # Test connection
        result = self.device_config.test_connection()
        
        # Verify connection was attempted
        mock_zk_class.assert_called_once_with(
            self.device_config.device_ip,
            port=self.device_config.device_port,
            timeout=self.device_config.timeout
        )
        mock_zk.connect.assert_called_once()
        mock_conn.disconnect.assert_called_once()
        
        # Verify result
        self.assertEqual(result['type'], 'ir.actions.client')
        self.assertEqual(result['tag'], 'display_notification')
    
    @patch('fingerprint_attendance.models.fingerprint_device_config.ZK_AVAILABLE', False)
    def test_connection_no_library(self):
        """Test connection when pyzk library is not available"""
        with self.assertRaises(UserError) as cm:
            self.device_config.test_connection()
        
        self.assertIn('pyzk library is not installed', str(cm.exception))
    
    @patch('fingerprint_attendance.models.fingerprint_device_config.ZK_AVAILABLE', True)
    @patch('fingerprint_attendance.models.fingerprint_device_config.ZK')
    def test_sync_attendance_data(self, mock_zk_class):
        """Test attendance data synchronization"""
        # Create test employee
        employee = self.env['hr.employee'].create({
            'name': 'Test Employee',
            'device_user_id': '001',
        })
        
        # Mock ZK instance and connection
        mock_zk = MagicMock()
        mock_conn = MagicMock()
        mock_zk_class.return_value = mock_zk
        mock_zk.connect.return_value = mock_conn
        
        # Mock attendance data
        mock_attendance = MagicMock()
        mock_attendance.user_id = '001'
        mock_attendance.timestamp = '2024-01-01 08:00:00'
        mock_attendance.punch = 0  # Check-in
        
        mock_user = MagicMock()
        mock_user.user_id = '001'
        mock_user.name = 'Test Employee'
        
        mock_conn.get_attendance.return_value = [mock_attendance]
        mock_conn.get_users.return_value = [mock_user]
        
        # Test sync
        result = self.device_config.sync_attendance_data()
        
        # Verify sync was performed
        mock_zk.connect.assert_called_once()
        mock_conn.get_attendance.assert_called_once()
        mock_conn.get_users.assert_called_once()
        mock_conn.disconnect.assert_called_once()
        
        # Verify result
        self.assertEqual(result['type'], 'ir.actions.client')
        self.assertEqual(result['tag'], 'display_notification')
        
        # Verify attendance record was created
        attendance = self.env['hr.attendance'].search([
            ('employee_id', '=', employee.id),
            ('device_attendance_id', '=', '001')
        ])
        self.assertEqual(len(attendance), 1)
    
    def test_cron_sync_attendance(self):
        """Test cron job for automatic sync"""
        # Create another device
        device2 = self.env['fingerprint.device.config'].create({
            'name': 'Test Device 2',
            'device_ip': '*************',
            'device_port': 4370,
            'auto_sync': False,  # This should not be synced
        })
        
        with patch.object(self.device_config, 'sync_attendance_data') as mock_sync:
            # Run cron job
            self.env['fingerprint.device.config'].cron_sync_attendance()
            
            # Verify only active auto-sync devices were synced
            mock_sync.assert_called_once()
    
    def test_create_sync_log(self):
        """Test sync log creation"""
        # Create sync log
        self.device_config._create_sync_log('success', 'Test message', {'test': 'data'})
        
        # Verify log was created
        log = self.env['fingerprint.sync.log'].search([
            ('device_config_id', '=', self.device_config.id)
        ])
        self.assertEqual(len(log), 1)
        self.assertEqual(log.status, 'success')
        self.assertEqual(log.message, 'Test message')
        self.assertIn('test', log.details)
