# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class HrEmployee(models.Model):
    _inherit = 'hr.employee'

    device_user_id = fields.Char(
        string='Device User ID',
        help='User ID in the fingerprint device system'
    )
    fingerprint_enrolled = fields.Boolean(
        string='Fingerprint Enrolled',
        default=False,
        help='Indicates if employee fingerprint is enrolled in the device'
    )
    last_attendance_sync = fields.Datetime(
        string='Last Attendance Sync',
        readonly=True,
        help='Last time attendance was synced from fingerprint device'
    )
    
    @api.constrains('device_user_id')
    def _check_device_user_id_unique(self):
        """Ensure device user ID is unique"""
        for record in self:
            if record.device_user_id:
                existing = self.search([
                    ('device_user_id', '=', record.device_user_id),
                    ('id', '!=', record.id),
                    ('company_id', '=', record.company_id.id)
                ])
                if existing:
                    raise ValidationError(
                        _('Device User ID "%s" is already assigned to employee "%s"') % 
                        (record.device_user_id, existing.name)
                    )

    def action_sync_fingerprint_data(self):
        """Sync fingerprint data for this employee"""
        self.ensure_one()
        
        if not self.device_user_id:
            raise ValidationError(_('Please set Device User ID for this employee first'))
        
        # Find active fingerprint devices
        devices = self.env['fingerprint.device.config'].search([
            ('active', '=', True),
            ('company_id', '=', self.company_id.id)
        ])
        
        if not devices:
            raise ValidationError(_('No active fingerprint devices found'))
        
        # Sync from all active devices
        for device in devices:
            try:
                device.sync_attendance_data()
            except Exception as e:
                # Log error but continue with other devices
                device._create_sync_log('error', 
                    _('Failed to sync data for employee %s: %s') % (self.name, str(e)))
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Sync Completed'),
                'message': _('Fingerprint data sync completed for %s') % self.name,
                'type': 'success',
                'sticky': False,
            }
        }
