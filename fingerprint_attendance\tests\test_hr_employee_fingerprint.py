# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase
from odoo.exceptions import ValidationError
from unittest.mock import patch


class TestHrEmployeeFingerprint(TransactionCase):
    
    def setUp(self):
        super().setUp()
        self.employee = self.env['hr.employee'].create({
            'name': 'Test Employee',
        })
        
        self.device_config = self.env['fingerprint.device.config'].create({
            'name': 'Test Device',
            'device_ip': '*************',
            'device_port': 4370,
        })
    
    def test_device_user_id_assignment(self):
        """Test device user ID assignment"""
        self.employee.write({'device_user_id': '001'})
        self.assertEqual(self.employee.device_user_id, '001')
    
    def test_device_user_id_uniqueness(self):
        """Test device user ID uniqueness constraint"""
        # Create first employee with device user ID
        self.employee.write({'device_user_id': '001'})
        
        # Try to create second employee with same device user ID
        employee2 = self.env['hr.employee'].create({
            'name': 'Test Employee 2',
        })
        
        with self.assertRaises(ValidationError) as cm:
            employee2.write({'device_user_id': '001'})
        
        self.assertIn('already assigned', str(cm.exception))
    
    def test_device_user_id_uniqueness_different_companies(self):
        """Test device user ID uniqueness across different companies"""
        # Create second company
        company2 = self.env['res.company'].create({
            'name': 'Test Company 2',
        })
        
        # Set device user ID for first employee
        self.employee.write({'device_user_id': '001'})
        
        # Create employee in different company with same device user ID
        employee2 = self.env['hr.employee'].create({
            'name': 'Test Employee 2',
            'company_id': company2.id,
            'device_user_id': '001',  # Should be allowed in different company
        })
        
        # Should not raise validation error
        self.assertEqual(employee2.device_user_id, '001')
    
    def test_fingerprint_enrolled_default(self):
        """Test fingerprint enrolled default value"""
        self.assertFalse(self.employee.fingerprint_enrolled)
    
    def test_fingerprint_enrolled_assignment(self):
        """Test fingerprint enrolled assignment"""
        self.employee.write({'fingerprint_enrolled': True})
        self.assertTrue(self.employee.fingerprint_enrolled)
    
    @patch('fingerprint_attendance.models.fingerprint_device_config.FingerprintDeviceConfig.sync_attendance_data')
    def test_sync_fingerprint_data_success(self, mock_sync):
        """Test successful fingerprint data sync"""
        # Set device user ID
        self.employee.write({'device_user_id': '001'})
        
        # Mock successful sync
        mock_sync.return_value = True
        
        # Test sync action
        result = self.employee.action_sync_fingerprint_data()
        
        # Verify sync was called
        mock_sync.assert_called_once()
        
        # Verify result
        self.assertEqual(result['type'], 'ir.actions.client')
        self.assertEqual(result['tag'], 'display_notification')
    
    def test_sync_fingerprint_data_no_device_id(self):
        """Test sync without device user ID"""
        with self.assertRaises(ValidationError) as cm:
            self.employee.action_sync_fingerprint_data()
        
        self.assertIn('Device User ID', str(cm.exception))
    
    def test_sync_fingerprint_data_no_devices(self):
        """Test sync when no active devices exist"""
        # Set device user ID
        self.employee.write({'device_user_id': '001'})
        
        # Deactivate device
        self.device_config.write({'active': False})
        
        with self.assertRaises(ValidationError) as cm:
            self.employee.action_sync_fingerprint_data()
        
        self.assertIn('No active fingerprint devices', str(cm.exception))
    
    @patch('fingerprint_attendance.models.fingerprint_device_config.FingerprintDeviceConfig.sync_attendance_data')
    def test_sync_fingerprint_data_with_error(self, mock_sync):
        """Test sync with device error"""
        # Set device user ID
        self.employee.write({'device_user_id': '001'})
        
        # Mock sync error
        mock_sync.side_effect = Exception('Connection failed')
        
        # Test sync action (should not raise exception, but log error)
        result = self.employee.action_sync_fingerprint_data()
        
        # Verify result is still returned
        self.assertEqual(result['type'], 'ir.actions.client')
        
        # Verify error was logged
        log = self.env['fingerprint.sync.log'].search([
            ('device_config_id', '=', self.device_config.id),
            ('status', '=', 'error')
        ])
        self.assertEqual(len(log), 1)
        self.assertIn('Connection failed', log.message)
    
    def test_last_attendance_sync_field(self):
        """Test last attendance sync field"""
        # Initially should be empty
        self.assertFalse(self.employee.last_attendance_sync)
        
        # Should be readonly (cannot be set directly)
        # This field is updated by the sync process
        self.assertTrue(
            self.env['hr.employee']._fields['last_attendance_sync'].readonly
        )
