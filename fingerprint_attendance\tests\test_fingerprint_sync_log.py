# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase
from datetime import datetime, timedelta
from odoo import fields


class TestFingerprintSyncLog(TransactionCase):
    
    def setUp(self):
        super().setUp()
        self.device_config = self.env['fingerprint.device.config'].create({
            'name': 'Test Device',
            'device_ip': '*************',
            'device_port': 4370,
        })
    
    def test_sync_log_creation(self):
        """Test sync log creation"""
        log = self.env['fingerprint.sync.log'].create({
            'device_config_id': self.device_config.id,
            'status': 'success',
            'message': 'Test sync completed',
            'details': 'Test details',
        })
        
        self.assertEqual(log.device_config_id, self.device_config)
        self.assertEqual(log.status, 'success')
        self.assertEqual(log.message, 'Test sync completed')
        self.assertEqual(log.details, 'Test details')
    
    def test_display_name_computation(self):
        """Test display name computation"""
        log = self.env['fingerprint.sync.log'].create({
            'device_config_id': self.device_config.id,
            'status': 'success',
            'message': 'Test sync',
            'sync_date': datetime(2024, 1, 1, 12, 0, 0),
        })
        
        expected_name = 'Test Device - 2024-01-01 12:00:00 (Success)'
        self.assertEqual(log.display_name, expected_name)
    
    def test_name_get(self):
        """Test name_get method"""
        log = self.env['fingerprint.sync.log'].create({
            'device_config_id': self.device_config.id,
            'status': 'error',
            'message': 'Test error',
            'sync_date': datetime(2024, 1, 1, 12, 0, 0),
        })
        
        name_get_result = log.name_get()
        self.assertEqual(len(name_get_result), 1)
        self.assertEqual(name_get_result[0][0], log.id)
        self.assertIn('Test Device', name_get_result[0][1])
        self.assertIn('Error', name_get_result[0][1])
    
    def test_cleanup_old_logs(self):
        """Test cleanup of old log entries"""
        # Create old log (35 days ago)
        old_date = fields.Datetime.now() - timedelta(days=35)
        old_log = self.env['fingerprint.sync.log'].create({
            'device_config_id': self.device_config.id,
            'status': 'success',
            'message': 'Old sync',
            'sync_date': old_date,
        })
        
        # Create recent log (5 days ago)
        recent_date = fields.Datetime.now() - timedelta(days=5)
        recent_log = self.env['fingerprint.sync.log'].create({
            'device_config_id': self.device_config.id,
            'status': 'success',
            'message': 'Recent sync',
            'sync_date': recent_date,
        })
        
        # Run cleanup (default 30 days)
        deleted_count = self.env['fingerprint.sync.log'].cleanup_old_logs()
        
        # Verify old log was deleted
        self.assertEqual(deleted_count, 1)
        self.assertFalse(old_log.exists())
        self.assertTrue(recent_log.exists())
    
    def test_cleanup_custom_days(self):
        """Test cleanup with custom retention period"""
        # Create log 10 days ago
        old_date = fields.Datetime.now() - timedelta(days=10)
        log = self.env['fingerprint.sync.log'].create({
            'device_config_id': self.device_config.id,
            'status': 'success',
            'message': 'Test sync',
            'sync_date': old_date,
        })
        
        # Run cleanup with 7 days retention
        deleted_count = self.env['fingerprint.sync.log'].cleanup_old_logs(days=7)
        
        # Verify log was deleted
        self.assertEqual(deleted_count, 1)
        self.assertFalse(log.exists())
    
    def test_company_inheritance(self):
        """Test company field inheritance from device config"""
        # Create company
        company = self.env['res.company'].create({
            'name': 'Test Company',
        })
        
        # Update device config with company
        self.device_config.write({'company_id': company.id})
        
        # Create log
        log = self.env['fingerprint.sync.log'].create({
            'device_config_id': self.device_config.id,
            'status': 'success',
            'message': 'Test sync',
        })
        
        # Verify company inheritance
        self.assertEqual(log.company_id, company)
