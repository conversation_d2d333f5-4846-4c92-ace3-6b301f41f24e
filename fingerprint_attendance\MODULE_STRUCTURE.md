# هيكل الموديول | Module Structure

## نظرة عامة | Overview

هذا الموديول مصمم وفقاً لمعايير Odoo 15 Community Edition ويتضمن جميع الملفات والمجلدات المطلوبة لتكامل شامل مع أجهزة البصمة.

This module is designed according to Odoo 15 Community Edition standards and includes all required files and folders for comprehensive fingerprint device integration.

## هيكل المجلدات | Directory Structure

```
fingerprint_attendance/
├── __init__.py                     # ملف التهيئة الرئيسي
├── __manifest__.py                 # بيان الموديول
├── README.md                       # دليل المستخدم الشامل
├── CHANGELOG.md                    # سجل التغييرات
├── MODULE_STRUCTURE.md             # هذا الملف
├── requirements.txt                # متطلبات Python
│
├── data/                           # بيانات النظام
│   └── ir_cron_data.xml           # المهام المجدولة
│
├── doc/                            # التوثيق
│   ├── INSTALLATION.md            # دليل التثبيت التفصيلي
│   └── SECURITY.md                # دليل الأمان
│
├── i18n/                           # ملفات الترجمة
│   └── ar.po                      # الترجمة العربية
│
├── models/                         # النماذج
│   ├── __init__.py
│   ├── fingerprint_device_config.py    # إعدادات الأجهزة
│   ├── fingerprint_sync_log.py         # سجل المزامنة
│   ├── hr_employee.py                  # إضافات الموظفين
│   └── hr_attendance.py                # إضافات الحضور
│
├── security/                       # الأمان والصلاحيات
│   ├── ir.model.access.csv        # صلاحيات النماذج
│   └── security.xml               # مجموعات المستخدمين
│
├── static/                         # الملفات الثابتة
│   └── description/
│       ├── icon.png               # أيقونة الموديول
│       └── index.html             # وصف الموديول
│
├── tests/                          # الاختبارات
│   ├── __init__.py
│   ├── test_fingerprint_device_config.py
│   ├── test_fingerprint_sync_log.py
│   └── test_hr_employee_fingerprint.py
│
├── views/                          # الواجهات
│   ├── fingerprint_device_views.xml    # واجهات الأجهزة
│   ├── fingerprint_log_views.xml       # واجهات السجلات
│   ├── hr_employee_views.xml           # إضافات واجهات الموظفين
│   └── menu_views.xml                  # القوائم
│
└── wizard/                         # المعالجات
    ├── __init__.py
    ├── sync_attendance_wizard.py       # معالج المزامنة
    └── sync_attendance_wizard_views.xml # واجهات المعالج
```

## الملفات الرئيسية | Main Files

### 1. ملفات التكوين | Configuration Files

#### `__manifest__.py`
- بيان الموديول مع جميع المعلومات المطلوبة
- قائمة التبعيات والملفات
- معلومات الإصدار والمؤلف

#### `__init__.py`
- تهيئة الموديول
- استيراد النماذج والمعالجات
- دوال ما بعد التثبيت وقبل الإزالة

#### `requirements.txt`
- قائمة مكتبات Python المطلوبة
- إصدارات محددة للتوافق

### 2. النماذج | Models

#### `fingerprint_device_config.py`
**الغرض**: إدارة إعدادات أجهزة البصمة
**المميزات**:
- إعدادات الاتصال (IP، Port، Username، Password)
- إعدادات المزامنة (فترة، تلقائي/يدوي)
- اختبار الاتصال
- مزامنة البيانات
- إحصائيات المزامنة

#### `fingerprint_sync_log.py`
**الغرض**: تسجيل عمليات المزامنة
**المميزات**:
- تسجيل حالة المزامنة (نجح/تحذير/خطأ)
- تفاصيل العملية
- تنظيف السجلات القديمة
- ربط بالجهاز والشركة

#### `hr_employee.py`
**الغرض**: إضافات للموظفين
**المميزات**:
- معرف المستخدم في الجهاز
- حالة تسجيل البصمة
- تاريخ آخر مزامنة
- مزامنة بيانات موظف محدد

#### `hr_attendance.py`
**الغرض**: إضافات لسجلات الحضور
**المميزات**:
- معرف سجل الحضور في الجهاز
- اسم الجهاز المصدر
- تمييز السجلات من الجهاز

### 3. الواجهات | Views

#### `fingerprint_device_views.xml`
- واجهة إدارة الأجهزة (نموذج، قائمة، بحث)
- أزرار اختبار الاتصال والمزامنة
- إحصائيات المزامنة

#### `fingerprint_log_views.xml`
- واجهة عرض سجلات المزامنة
- فلاتر حسب التاريخ والحالة
- تجميع حسب الجهاز والحالة

#### `hr_employee_views.xml`
- إضافة تبويب إعدادات البصمة للموظفين
- حقول معرف الجهاز وحالة البصمة
- زر مزامنة بيانات الموظف

#### `menu_views.xml`
- قوائم التنقل المنظمة
- ترتيب منطقي للوصول للمميزات

### 4. المعالجات | Wizards

#### `sync_attendance_wizard.py`
**الغرض**: معالج المزامنة اليدوية
**المميزات**:
- اختيار الأجهزة للمزامنة
- خيارات المزامنة المتقدمة
- عرض نتائج المزامنة

### 5. الأمان | Security

#### `security.xml`
- مجموعة "Fingerprint User": عرض وتشغيل
- مجموعة "Fingerprint Manager": إدارة كاملة
- قواعد أمان متعددة الشركات

#### `ir.model.access.csv`
- صلاحيات تفصيلية لكل نموذج
- تحديد الوصول (قراءة، كتابة، إنشاء، حذف)

### 6. البيانات | Data

#### `ir_cron_data.xml`
- مهمة المزامنة التلقائية (كل 5 دقائق)
- مهمة تنظيف السجلات القديمة (يومياً)

### 7. الاختبارات | Tests

#### `test_fingerprint_device_config.py`
- اختبار إنشاء وتكوين الأجهزة
- اختبار التحقق من البيانات
- اختبار الاتصال والمزامنة

#### `test_fingerprint_sync_log.py`
- اختبار إنشاء السجلات
- اختبار تنظيف السجلات القديمة
- اختبار وراثة الشركة

#### `test_hr_employee_fingerprint.py`
- اختبار إضافات الموظفين
- اختبار فرادة معرف الجهاز
- اختبار مزامنة بيانات الموظف

### 8. التوثيق | Documentation

#### `README.md`
- دليل شامل بالعربية والإنجليزية
- خطوات التثبيت والإعداد
- استكشاف الأخطاء
- أمثلة الاستخدام

#### `INSTALLATION.md`
- خطوات التثبيت التفصيلية
- متطلبات النظام
- إعداد البيئة
- اختبار التثبيت

#### `SECURITY.md`
- إرشادات الأمان الشاملة
- أفضل الممارسات
- إعدادات الشبكة الآمنة
- مراقبة الأمان

#### `CHANGELOG.md`
- سجل التغييرات والإصدارات
- المميزات الجديدة
- إصلاح الأخطاء
- التحسينات

### 9. الترجمة | Translation

#### `ar.po`
- ترجمة كاملة للعربية
- جميع النصوص والرسائل
- واجهات المستخدم

## المميزات التقنية | Technical Features

### 1. التوافق | Compatibility
- Odoo 15 Community Edition
- Python 3.7+
- مكتبة pyzk للاتصال بأجهزة ZKTeco
- دعم أجهزة TCP/IP المختلفة

### 2. الأداء | Performance
- مزامنة فعالة للبيانات
- تجنب التكرار في السجلات
- تنظيف تلقائي للسجلات القديمة
- معالجة الأخطاء المتقدمة

### 3. الأمان | Security
- تشفير كلمات المرور
- نظام صلاحيات متدرج
- قواعد أمان متعددة الشركات
- تسجيل شامل للعمليات

### 4. سهولة الاستخدام | Usability
- واجهات بديهية وسهلة
- رسائل خطأ واضحة
- دعم الترجمة
- توثيق شامل

### 5. القابلية للصيانة | Maintainability
- كود منظم ومعلق
- اختبارات شاملة
- توثيق تفصيلي
- هيكل واضح

## خطوات التطوير المستقبلية | Future Development

### المرحلة 1.1
- دعم أجهزة إضافية
- تحسين الأداء
- مميزات تقارير متقدمة

### المرحلة 1.2
- واجهة ويب للمراقبة
- تنبيهات في الوقت الفعلي
- تكامل مع أنظمة خارجية

### المرحلة 2.0
- دعم البصمة الحيوية المتقدمة
- تحليلات الحضور الذكية
- تطبيق موبايل

---

**ملاحظة**: هذا الهيكل مصمم ليكون قابلاً للتوسع والصيانة مع الحفاظ على معايير Odoo.

**Note**: This structure is designed to be scalable and maintainable while adhering to Odoo standards.
