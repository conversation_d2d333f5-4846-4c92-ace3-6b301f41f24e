# Fingerprint Attendance Integration for Odoo 15

## نظرة عامة | Overview

هذا الموديول يوفر تكامل شامل بين أودوو 15 وأجهزة البصمة لتتبع الحضور والانصراف تلقائياً.

This module provides comprehensive integration between Odoo 15 and fingerprint attendance devices for automatic attendance tracking.

## المميزات | Features

### العربية
- **الاتصال المباشر**: يتصل مباشرة مع أجهزة البصمة عبر TCP/IP
- **المزامنة التلقائية**: مزامنة تلقائية للبيانات كل فترة زمنية محددة
- **المزامنة اليدوية**: إمكانية المزامنة اليدوية عند الحاجة
- **دعم متعدد الأجهزة**: يدعم عدة أجهزة بصمة في نفس الوقت
- **سجل شامل**: تسجيل جميع العمليات والأخطاء
- **واجهة سهلة**: واجهة مستخدم بسيطة وسهلة الاستخدام
- **دعم الترجمة**: يدعم العربية والإنجليزية
- **أمان متقدم**: نظام صلاحيات متقدم لحماية البيانات

### English
- **Direct Connection**: Direct TCP/IP connection to fingerprint devices
- **Auto Synchronization**: Automatic data sync at configurable intervals
- **Manual Sync**: Manual synchronization option when needed
- **Multi-Device Support**: Support for multiple fingerprint devices
- **Comprehensive Logging**: Complete logging of all operations and errors
- **User-Friendly Interface**: Simple and easy-to-use interface
- **Translation Support**: Supports Arabic and English
- **Advanced Security**: Advanced permission system for data protection

## المتطلبات | Requirements

### النظام | System
- Odoo 15 Community Edition
- Python 3.7+
- Network access to fingerprint devices
- VPS or local server with LAN connectivity

### المكتبات | Libraries
```bash
pip install pyzk
```

### الأجهزة المدعومة | Supported Devices
- ZKTeco devices
- Any TCP/IP enabled attendance device
- Devices supporting standard attendance protocols

## التثبيت | Installation

### 1. تحضير البيئة | Environment Setup

```bash
# Install required Python library
pip install pyzk

# Or using pip3
pip3 install pyzk
```

### 2. تثبيت الموديول | Module Installation

1. **نسخ الملفات | Copy Files**
   ```bash
   # Copy the module to Odoo addons directory
   cp -r fingerprint_attendance /path/to/odoo/addons/
   ```

2. **تحديث قائمة التطبيقات | Update Apps List**
   - Go to Apps menu in Odoo
   - Click "Update Apps List"
   - Search for "Fingerprint Attendance Integration"

3. **تثبيت الموديول | Install Module**
   - Click "Install" button
   - Wait for installation to complete

### 3. الإعداد الأولي | Initial Configuration

1. **إعداد الصلاحيات | Setup Permissions**
   - Go to Settings > Users & Companies > Users
   - Assign "Fingerprint Manager" group to administrators
   - Assign "Fingerprint User" group to HR users

2. **إعداد الجهاز | Device Configuration**
   - Go to HR > Configuration > Fingerprint Attendance > Fingerprint Devices
   - Click "Create" to add new device
   - Fill in device details:
     - Device Name: اسم مميز للجهاز
     - Device IP Address: عنوان IP للجهاز
     - Port: المنفذ (عادة 4370)
     - Username/Password: إذا كان مطلوب
     - Sync Interval: فترة المزامنة بالدقائق

3. **اختبار الاتصال | Test Connection**
   - Click "Test Connection" button
   - Verify successful connection

## الاستخدام | Usage

### إعداد الموظفين | Employee Setup

1. **ربط الموظفين بالجهاز | Link Employees to Device**
   - Go to HR > Employees
   - Open employee record
   - Go to "Fingerprint Settings" tab
   - Set "Device User ID" (must match device user ID)
   - Mark "Fingerprint Enrolled" if applicable

### المزامنة | Synchronization

#### المزامنة التلقائية | Automatic Sync
- يتم تشغيلها تلقائياً حسب الفترة المحددة
- Runs automatically based on configured interval
- Can be enabled/disabled per device

#### المزامنة اليدوية | Manual Sync
1. **من إعدادات الجهاز | From Device Settings**
   - Go to device configuration
   - Click "Sync Now" button

2. **من معالج المزامنة | From Sync Wizard**
   - Go to HR > Configuration > Fingerprint Attendance > Manual Sync
   - Select devices to sync
   - Click "Start Sync"

### مراقبة السجلات | Monitoring Logs

1. **عرض السجلات | View Logs**
   - Go to HR > Configuration > Fingerprint Attendance > Sync Logs
   - Filter by date, device, or status
   - View detailed error messages

2. **تنظيف السجلات | Log Cleanup**
   - Old logs are automatically cleaned up after 30 days
   - Can be configured in cron job settings

## استكشاف الأخطاء | Troubleshooting

### مشاكل الاتصال | Connection Issues

1. **فحص الشبكة | Network Check**
   ```bash
   # Test network connectivity
   ping [device_ip]
   telnet [device_ip] [port]
   ```

2. **فحص إعدادات الجهاز | Device Settings Check**
   - Verify IP address and port
   - Check device network settings
   - Ensure device is powered on

3. **فحص المكتبة | Library Check**
   ```bash
   # Verify pyzk installation
   python -c "import zk; print('pyzk is installed')"
   ```

### مشاكل المزامنة | Sync Issues

1. **فحص معرفات الموظفين | Employee ID Check**
   - Ensure Device User ID matches device records
   - Check for duplicate Device User IDs

2. **فحص الصلاحيات | Permission Check**
   - Verify user has proper permissions
   - Check company access rights

3. **فحص السجلات | Log Check**
   - Review sync logs for error details
   - Check Odoo server logs

### رسائل الخطأ الشائعة | Common Error Messages

| خطأ | Error | الحل | Solution |
|-----|-------|------|----------|
| Connection timeout | انتهت مهلة الاتصال | فحص الشبكة والجهاز | Check network and device |
| pyzk not found | مكتبة pyzk غير موجودة | تثبيت المكتبة | Install pyzk library |
| Duplicate Device User ID | معرف مستخدم مكرر | تغيير المعرف | Change user ID |
| Permission denied | ممنوع الوصول | فحص الصلاحيات | Check permissions |

## الإعدادات المتقدمة | Advanced Configuration

### إعداد المهام المجدولة | Cron Job Configuration

1. **تعديل فترة المزامنة | Modify Sync Interval**
   - Go to Settings > Technical > Automation > Scheduled Actions
   - Find "Sync Fingerprint Attendance Data"
   - Modify interval as needed

2. **تعديل تنظيف السجلات | Modify Log Cleanup**
   - Find "Cleanup Old Fingerprint Sync Logs"
   - Modify retention period

### إعدادات الأمان | Security Settings

1. **تخصيص الصلاحيات | Custom Permissions**
   - Create custom user groups
   - Assign specific model access rights
   - Configure record rules

2. **تقييد الوصول | Access Restrictions**
   - Limit access by company
   - Restrict device configuration access
   - Control sync operation permissions

## الدعم والصيانة | Support & Maintenance

### النسخ الاحتياطي | Backup

```bash
# Backup attendance data
pg_dump -U odoo_user -h localhost -t hr_attendance odoo_db > attendance_backup.sql

# Backup device configurations
pg_dump -U odoo_user -h localhost -t fingerprint_device_config odoo_db > device_config_backup.sql
```

### التحديث | Updates

1. **تحديث الموديول | Module Update**
   - Go to Apps menu
   - Find "Fingerprint Attendance Integration"
   - Click "Upgrade"

2. **تحديث المكتبات | Library Update**
   ```bash
   pip install --upgrade pyzk
   ```

### المراقبة | Monitoring

1. **مراقبة الأداء | Performance Monitoring**
   - Monitor sync frequency
   - Check error rates
   - Review processing times

2. **مراقبة الموارد | Resource Monitoring**
   - Monitor CPU usage during sync
   - Check memory consumption
   - Monitor network traffic

## الترخيص | License

هذا الموديول مرخص تحت رخصة LGPL-3.

This module is licensed under LGPL-3 license.

## المساهمة | Contributing

نرحب بالمساهمات والتحسينات على هذا الموديول.

Contributions and improvements to this module are welcome.

## الدعم الفني | Technical Support

للحصول على الدعم الفني، يرجى التواصل معنا.

For technical support, please contact us.

---

**ملاحظة**: هذا الموديول مصمم للعمل في البيئات المحلية (LAN) ولا يتطلب اتصال بالإنترنت.

**Note**: This module is designed to work in local environments (LAN) and does not require internet connectivity.
